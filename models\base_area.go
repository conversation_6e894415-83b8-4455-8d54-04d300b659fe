package models

type BaseArea struct {
	AreaId       int    `xorm:"not null pk autoincr comment('索引ID') INT(11)"`
	AreaName     string `xorm:"not null comment('地区名称') VARCHAR(50)"`
	AreaParentId int    `xorm:"not null default 0 comment('地区父ID') index INT(11)"`
	AreaSort     int    `xorm:"not null default 0 comment('排序') TINYINT(3)"`
	AreaDeep     int    `xorm:"not null default 1 comment('地区深度，从1开始') TINYINT(1)"`
	AreaRegion   string `xorm:"default 'NULL' comment('大区名称') VARCHAR(3)"`
}
