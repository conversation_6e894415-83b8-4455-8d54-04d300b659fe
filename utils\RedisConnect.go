package utils

import (
	"github.com/go-redis/redis"
	"github.com/go-xorm/xorm"
	"github.com/limitedlee/microservice/common/config"
	"github.com/spf13/cast"
	"strconv"
	"time"
)

//redis链接
func ConnectClusterRedis() *redis.Client {
	DB, _ := strconv.Atoi(config.GetString("redis.DB"))
	client := redis.NewClient(&redis.Options{
		Addr:     config.GetString("redis.Addr"),
		Password: config.GetString("redis.Password"),
		DB:       DB,
	})
	////影响性能问题的判断--先去掉
	//_, err := client.Ping().Result()
	//if err != nil {
	//
	//	logger.Error("打开redis连接报错:" + err.Error())
	//	panic(err)
	//}
	return client
}

//批量删除rediskey
func RedisDealKeys(Patternkeys string) {
	client := ConnectClusterRedis()
	defer client.Close()
	keylist := client.Keys(Patternkeys).Val()
	//keylist := ClusterRedisKey(Patternkeys, client) //redis集群模式
	for _, v := range keylist {
		client.Del(v)
	}
}

//专属于管易导入库存删除key，删除所有变成-1的key
func RedisDealKeysByTTL(Patternkeys string) {
	client := ConnectClusterRedis()
	defer client.Close()

	//var keylist []string
	//dbsize := client.DBSize().Val()
	//keys, _ := client.Scan(0, Patternkeys, dbsize).Val()
	//keylist = append(keylist, keys...)
	keylist := client.Keys(Patternkeys).Val()
	//keylist := ClusterRedisKey(Patternkeys, client) //redis集群模式
	for _, v := range keylist {
		//删除冻结的key
		if client.TTL(v).Val() == -1*time.Second {
			client.Del(v)
		}
	}
}

//
////集群
//func ConnectClusterRedis() *redis.ClusterClient {
//	address := config.GetString("redis.cluster")
//	pwd := config.GetString("redis.cluster.pwd")
//	addrs := strings.Split(address, "|")
//	client := redis.NewClusterClient(&redis.ClusterOptions{
//		Addrs:    addrs,
//		Password: pwd,
//	})
//	_, err := client.Ping().Result()
//	if err != nil {
//		panic(err)
//	}
//	return client
//}
//
////根据key模糊获取redis集群(多主多从)的所有key
//func ClusterRedisKey(Patternkeys string, client *redis.ClusterClient) []string {
//	pwd := config.GetString("redis.cluster.pwd")
//	clusterSlots, err := client.ClusterSlots().Result()
//	if err != nil {
//		return []string{}
//	}
//	allKey := make([]string, 0)
//	for _, v := range clusterSlots {
//		vclient := redis.NewClient(&redis.Options{
//			Addr:     v.Nodes[0].Addr,
//			Password: pwd,
//		})
//		hv := vclient.Keys(Patternkeys).Val()
//		for _, v := range hv {
//			allKey = append(allKey, v)
//		}
//		vclient.Close()
//	}
//	return allKey
//}
var (
	redisHandle *redis.Client
	engine      *xorm.Engine
)

func CloseDB() {
	engine.Close()
	redisHandle.Close()
}
func SetupDB() {
	engine = NewDbConn()
	redisHandle = GetRedisConn()
}

//获取redis集群客户端
func GetRedisConn() *redis.Client {
	if redisHandle != nil {
		_, err := redisHandle.Ping().Result()
		//glog.Info("redis connections: ", redisHandle.PoolStats().TotalConns)
		if err == nil {
			return redisHandle
		}
	}

	var db = cast.ToInt(config.GetString("redis.DB"))
	var addr = config.GetString("redis.Addr")
	var pwd = config.GetString("redis.Password")

	//glog.Info("redis connections:" + addr + ",paw:" + pwd)

	redisHandle = redis.NewClient(&redis.Options{
		Addr:         addr,
		Password:     pwd,
		DB:           db,
		MinIdleConns: 28,
		IdleTimeout:  20 * time.Second,
		PoolSize:     512,
		MaxConnAge:   20 * time.Second,
	})
	_, err := redisHandle.Ping().Result()
	if err != nil {
		panic(err)
	}

	return redisHandle
}

func NewDbConn(dataSourceName ...string) *xorm.Engine {
	var err error

	if engine != nil {
		//if err = engine.DB().Ping(); err == nil {
		//	return engine
		//}
		return engine
	}

	var mySqlStr string
	if len(dataSourceName) == 1 {
		mySqlStr = dataSourceName[0]
	} else {
		mySqlStr = config.GetString("mysql.dc_order")
	}

	//mySqlStr = "root:d&!89iCEGKOuVHkT@(123.57.167.33:23306)/dc_order?charset=utf8mb4"
	//mySqlStr = "root:XjIrQepuHn7u^E8D@(39.106.30.60:13306)/dc_order?charset=utf8mb4"

	//mySqlStr = "readonly:fdSDF3er(34@(10.1.1.242:5532)/dc_product?charset=utf8"
	//mySqlStr = "root:Rp000000@(10.1.1.245:3306)/dc_product?charset=utf8"
	//mySqlStr = "root:Rp000000@(192.168.254.11:3306)/dc_product?charset=utf8"
	// mySqlStr = "dbuser:ZQA!2sxQQ@(10.1.1.242:5532)/dc_product?charset=utf8mb4"

	if len(mySqlStr) == 0 {
		panic("can't find mysql url")
	}

	engine, err = xorm.NewEngine("mysql", mySqlStr)
	if err != nil {
		panic(err)
	}

	//空闲关闭时间
	engine.SetConnMaxLifetime(120 * time.Second)
	//最大空闲连接
	engine.SetMaxIdleConns(10)
	//最大连接数
	engine.SetMaxOpenConns(1000)

	engine.SetTZLocation(time.Local)

	return engine
}
