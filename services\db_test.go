package services

import (
	"database/sql"
	"github.com/go-xorm/xorm"
	"reflect"
	"testing"
)

func TestDatacenterMysqlConn(t *testing.T) {
	tests := []struct {
		name string
		want *xorm.Engine
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := DatacenterMysqlConn(); !reflect.DeepEqual(got, tt.want) {
				t.<PERSON>("DatacenterMysqlConn() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetDCOrderMysqlConn(t *testing.T) {
	tests := []struct {
		name string
		want *xorm.Engine
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetDCOrderMysqlConn(); !reflect.DeepEqual(got, tt.want) {
				t.<PERSON>("GetDCOrderMysqlConn() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetMssqlConn(t *testing.T) {
	tests := []struct {
		name string
		want *sql.DB
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetMssqlConn(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetMssqlConn() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetMysqlConn(t *testing.T) {
	tests := []struct {
		name string
		want *xorm.Engine
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetMysqlConn(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetMysqlConn() = %v, want %v", got, tt.want)
			}
		})
	}
}

func BenchmarkPulishMq(b *testing.B) {
	PulishMq("test004", "test001", "测试测试")
}
