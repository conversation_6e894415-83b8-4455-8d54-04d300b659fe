package models

import (
	"time"
)

type Warehouse struct {
	Id         int       `xorm:"not null pk autoincr comment('自增，仓库id') INT(11)"`
	Thirdid    string    `xorm:"not null default '''' comment('第三方仓库ID 例如a8id,管易ID') VARCHAR(50)"`
	Code       string    `xorm:"not null default '''' comment('仓库编号') VARCHAR(32)"`
	Name       string    `xorm:"not null default '''' comment('仓库名称') VARCHAR(32)"`
	Comefrom   int       `xorm:"not null default 1 comment('仓库归属(1-A8,2-管易)') INT(11)"`
	Level      int       `xorm:"not null default 1 comment('仓库等级(1-1级，2-2级，3-3级，4-4级，5-5级)') INT(11)"`
	Category   int       `xorm:"not null default 1 comment('仓库类型(1-中心仓，2-区域仓，3-门店仓，4-前置仓)') INT(11)"`
	Address    string    `xorm:"not null default '''' comment('仓库地址') VARCHAR(255)"`
	Contacts   string    `xorm:"not null default '''' comment('仓库联系人') VARCHAR(32)"`
	Tel        string    `xorm:"not null default '''' comment('仓库联系方式') VARCHAR(20)"`
	Status     int       `xorm:"not null default 0 comment('仓库状态（0-禁用，1-启用）') INT(11)"`
	Createdate time.Time `xorm:"not null default 'current_timestamp()' comment('创建时间') DATETIME"`
	Lastdate   time.Time `xorm:"not null default 'current_timestamp()' comment('最后更新时间') DATETIME"`
	Subsystem  int       `xorm:"not null default 0 comment('所属系统 0:默认,1:ERP,2:子龙') INT(11)"`
	Ratio      int       `xorm:"not null default 0 comment('仓库比例') INT(11)"`
	Lng        int       `xorm:"not null default 0 comment('仓库经度') INT(20)"`
	Lat        int       `xorm:"not null default 0 comment('仓库纬度') INT(20)"`
}

//跑redis数据用的结构体
type Warehouse1 struct {
	Id         int       `xorm:"not null pk autoincr comment('自增，仓库id') INT(11)"`
	Thirdid    string    `xorm:"not null default '''' comment('第三方仓库ID 例如a8id,管易ID') VARCHAR(50)"`
	Code       string    `xorm:"not null default '''' comment('仓库编号') VARCHAR(32)"`
	Name       string    `xorm:"not null default '''' comment('仓库名称') VARCHAR(32)"`
	Comefrom   int       `xorm:"not null default 1 comment('仓库归属(1-A8,2-管易)') INT(11)"`
	Level      int       `xorm:"not null default 1 comment('仓库等级(1-1级，2-2级，3-3级，4-4级，5-5级)') INT(11)"`
	Category   int       `xorm:"not null default 1 comment('仓库类型(1-中心仓，2-区域仓，3-门店仓，4-前置仓)') INT(11)"`
	Address    string    `xorm:"not null default '''' comment('仓库地址') VARCHAR(255)"`
	Contacts   string    `xorm:"not null default '''' comment('仓库联系人') VARCHAR(32)"`
	Tel        string    `xorm:"not null default '''' comment('仓库联系方式') VARCHAR(20)"`
	Status     int       `xorm:"not null default 0 comment('仓库状态（0-禁用，1-启用）') INT(11)"`
	Createdate time.Time `xorm:"not null default 'current_timestamp()' comment('创建时间') DATETIME"`
	Lastdate   time.Time `xorm:"not null default 'current_timestamp()' comment('最后更新时间') DATETIME"`
	Subsystem  int       `xorm:"not null default 0 comment('所属系统 0:默认,1:ERP,2:子龙') INT(11)"`
	Ratio      int       `xorm:"not null default 0 comment('仓库比例') INT(11)"`
	Lng        int       `xorm:"not null default 0 comment('仓库经度') INT(20)"`
	Lat        int       `xorm:"not null default 0 comment('仓库纬度') INT(20)"`
	ShopId     string    `xorm:"default 'NULL' comment('关联的门店id') VARCHAR(100)"`
}
