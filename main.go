package main

import (
	"_/proto/ic"
	"_/services"
	"_/utils"
	"fmt"
	"github.com/BurntSushi/toml"
	"github.com/limitedlee/microservice/micro"
	"github.com/maybgit/glog"
	"github.com/robfig/cron"
	rpkit "github.com/tricobbler/rp-kit"
	"google.golang.org/grpc/reflection"
	"time"
)

var AppSettings AppSetting

type AppSetting struct {
	Server struct {
		ServerTag string //服务标记
	}
}

func init() {
	sh, _ := time.LoadLocation("Asia/Shanghai")
	time.Local = sh
	toml.DecodeFile("appsetting.toml", &AppSettings)

	if rpkit.EnvCanCron() {
		glog.Info("EnvCanCron true")
		//定时任务
		task := cron.NewWithLocation(time.Now().In(sh).Location())
		//task.AddFunc("0 0/5 * * * ?", services.SyncA8VirtualGoodsQty)      // 每5分钟拉取一次库存
		if AppSettings.Server.ServerTag == "" || AppSettings.Server.ServerTag == "1" {
			task.AddFunc("0 0 23 * * ? ", services.SyncA8VirtualGoodsQtyForQZC) // 每天23点拉取一次库存
		}
		if AppSettings.Server.ServerTag == "" || AppSettings.Server.ServerTag == "2" {
			task.AddFunc("0 0 23 * * ? ", services.SyncA8VirtualGoodsQtyForQZCVirtual) // 每天23点拉取一次库存
		}
		task.AddFunc("0/2 * * * * ?", services.ToProvideSyncStock) // 每2秒分钟同步一次库存
		//task.AddFunc("0/2 * * * * ?", services.OmsAutoStock)               // 一直处理OMS推过来的库存
		task.Start()
	} else {
		glog.Info("EnvCanCron false")
	}

}

func main() {
	micro := micro.MicService{}
	micro.NewServer()

	//初始化mysql和redis连接
	utils.SetupDB()
	//关闭mysql和redis连接
	defer utils.CloseDB()
	glog.Info(123)
	//服务反射，便于查看grpc的状态
	reflection.Register(micro.GrpcServer)
	//服务注册
	ic.RegisterInventoryServiceServer(micro.GrpcServer, &services.StockService{})
	ic.RegisterZilongServiceServer(micro.GrpcServer, &services.ZilongService{})
	fmt.Println("inventory-center服务启动...master")
	micro.Start()
}
