## dockerfile  .gitlab-ci ŵ
stages:
    - deploy
 
deploy_dev_job:
    stage: deploy
    script:
        - docker build -t inventorycenter .
        - docker login --username=rppet *************:5000 --password=H2cB3^A@h^K65dAB99kj
        - docker tag inventorycenter *************:5000/datacenter/inventorycenter:latest && docker push *************:5000/datacenter/inventorycenter:latest && docker rmi *************:5000/datacenter/inventorycenter:latest
        - if [ "$(docker ps -a -q -f name=inventorycenter)" ]; then docker rm -f inventorycenter; fi
        - docker run -d -p 11007:11007 --net=host --restart=always -e ASPNETCORE_ENVIRONMENT='Staging' -v /var/log/docker:/logs -v /config/inventorycenter/appsetting.toml:/appsetting.toml --name inventorycenter inventorycenter
        