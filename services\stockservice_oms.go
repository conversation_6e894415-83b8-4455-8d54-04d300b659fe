package services

import (
	"_/dtos"
	"_/models"
	"_/proto/ic"
	"context"
	"fmt"

	"github.com/golang/protobuf/ptypes/empty"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

//同步oms库存
func (t StockService) SyncOmsStock(ctx context.Context, req *ic.SyncOmsStockRequest) (*empty.Empty, error) {
	glog.Infof("StockService/SyncOmsStock 收到全量同步oms库存 %s", kit.JsonEncode(req))
	if len(req.List) == 0 {
		return &empty.Empty{}, nil
	}

	//初始化数据库
	conn := GetMysqlConn()
	defer conn.Close()
	session := conn.NewSession()
	defer session.Close()

	// 查询仓库信息
	var warehouseList []models.Warehouse
	err := session.Table("dc_dispatch.warehouse").Where("code = ? AND category in (4 , 5)", req.WarehouseCode).Find(&warehouseList)
	if err != nil {
		glog.Errorf("StockService/SyncOmsStock 查询仓库信息异常:%+v %s", err, req.WarehouseCode)
		return nil, err
	}

	if len(warehouseList) == 0 {
		glog.Errorf("StockService/SyncOmsStock 仓库不存在:%s", kit.JsonEncode(req))
		return nil, fmt.Errorf("仓库不存在:%s", req.WarehouseCode)
	}

	var skuList []models.SkuThird
	thirdSkuIds := make([]string, 0, len(req.List))
	thirdSkuMap := make(map[string]*ic.SkuStock, len(req.List))
	for _, v := range req.List {
		thirdSkuIds = append(thirdSkuIds, v.ThirdSkuId)
		thirdSkuMap[v.ThirdSkuId] = v
	}
	err = session.Table("dc_product.gj_sku_third").Where("erp_id=2").In("third_sku_id", thirdSkuIds).Select("id,sku_id,product_id,third_sku_id").Find(&skuList)
	if err != nil {
		glog.Errorf("StockService/SyncOmsStock 第三方货号换取skuId异常:%+v %s", err, req.WarehouseCode)
		return nil, err
	}

	skuIds := make([]string, 0, len(skuList))
	stockList := make([]dtos.GoodsStock, 0, len(skuList))
	for _, v := range skuList {
		thirdSku, ok := thirdSkuMap[v.ThirdSkuId]
		if !ok {
			glog.Errorf("StockService/SyncOmsStock 根据货号找不到对应商品信息 %s %s", v.ThirdSkuId, req.WarehouseCode)
			continue
		}
		skuIds = append(skuIds, cast.ToString(v.SkuId))
		stockList = append(stockList, dtos.GoodsStock{
			SpuId:   cast.ToString(v.ProductId),
			GoodsId: cast.ToString(v.SkuId),
			Stock:   int(thirdSku.Stock),
		})
	}
	// 写入库存
	writeStock(warehouseList[0], skuIds, stockList)
	return &empty.Empty{}, nil
}

//差异同步oms库存
func (t StockService) SyncOmsDiffStock(ctx context.Context, req *ic.SyncOmsDiffStockRequest) (*empty.Empty, error) {
	glog.Infof("StockService/SyncOmsDiffStock 收到增量同步oms库存 %s", kit.JsonEncode(req))

	//初始化数据库
	conn := GetMysqlConn()
	defer conn.Close()

	// 查询仓库信息
	var warehouseList []models.Warehouse
	err := conn.Table("dc_dispatch.warehouse").Where("code = ? AND category in (4 , 5)", req.WarehouseCode).Find(&warehouseList)
	if err != nil {
		glog.Errorf("StockService/SyncOmsDiffStock 查询仓库信息异常:%+v %s", err, req.WarehouseCode)
		return nil, err
	}

	if len(warehouseList) == 0 {
		glog.Errorf("StockService/SyncOmsDiffStock 仓库不存在:%s", kit.JsonEncode(req))
		return nil, fmt.Errorf("仓库不存在:%s", req.WarehouseCode)
	}

	// 根据货号换取skuId
	thirdSkuIds := make([]string, 0, len(req.List))
	for _, v := range req.List {
		thirdSkuIds = append(thirdSkuIds, v.ThirdSkuId)
	}
	var thirdSkuList []models.SkuThird
	err = conn.Table("dc_product.gj_sku_third").In("third_sku_id", thirdSkuIds).Find(&thirdSkuList)
	if err != nil {
		glog.Errorf("StockService/SyncOmsDiffStock 根据货号换取skuId异常:%+v %s", err, req.WarehouseCode)
		return nil, err
	}
	thirdSkuMap := make(map[string]models.SkuThird, len(thirdSkuList))
	skuIds := make([]string, 0, len(thirdSkuList))
	for _, v := range thirdSkuList {
		thirdSkuMap[v.ThirdSkuId] = v
		skuIds = append(skuIds, cast.ToString(v.SkuId))
	}
	for _, v := range req.List {
		if _, ok := thirdSkuMap[v.ThirdSkuId]; !ok {
			glog.Errorf("StockService/SyncOmsDiffStock 商品编码未绑定阿闻商品:%s %s", v.ThirdSkuId, kit.JsonEncode(req))
			continue
		}
	}

	stockList := make([]dtos.GoodsStock, 0, len(thirdSkuList))
	for _, v := range req.List {
		thirdSku := thirdSkuMap[v.ThirdSkuId]
		if thirdSku.SkuId == 0 {
			continue
		}

		stockList = append(stockList, dtos.GoodsStock{
			SpuId:   cast.ToString(thirdSku.ProductId),
			GoodsId: cast.ToString(thirdSku.SkuId),
			Stock:   int(v.Stock),
		})
	}

	if len(stockList) > 0 {
		// 通过增量方式写入库存
		writeStock(warehouseList[0], skuIds, stockList, true)
	} else {
		glog.Info(warehouseList[0], " 没有需要写入的数据")
	}

	return &empty.Empty{}, nil
}
