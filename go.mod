module _

go 1.12

require (
	github.com/BurntSushi/toml v0.3.1
	github.com/agiledragon/gomonkey v2.0.2+incompatible // indirect
	github.com/denisenkom/go-mssqldb v0.0.0-20190707035753-2be1aa521ff4
	github.com/go-redis/redis v6.15.9+incompatible
	github.com/go-sql-driver/mysql v1.5.0
	github.com/go-xorm/xorm v0.7.9
	github.com/golang/protobuf v1.5.1
	github.com/google/uuid v1.2.0
	github.com/labstack/echo/v4 v4.1.16
	github.com/limitedlee/microservice v0.1.0
	github.com/maybgit/glog v0.0.0-20210317132547-ba96c37023fe
	github.com/maybgit/pbgo v0.0.0-20200601050928-85c4ece4a248
	github.com/olivere/elastic/v7 v7.0.22
	github.com/robfig/cron v1.2.0
	github.com/shopspring/decimal v1.2.0
	github.com/sony/sonyflake v1.0.0
	github.com/spf13/cast v1.3.1
	github.com/streadway/amqp v1.0.0
	github.com/stretchr/testify v1.6.1
	github.com/tealeg/xlsx v1.0.5
	github.com/tricobbler/mqgo v0.3.24
	github.com/tricobbler/rp-kit v0.0.0-20210413075252-45df7834f17a
	golang.org/x/crypto v0.0.0-20210711020723-a769d52b0f97 // indirect
	golang.org/x/net v0.0.0-20210726213435-c6fcb2dbf985 // indirect
	google.golang.org/genproto v0.0.0-20210317182105-75c7a8546eb9
	google.golang.org/grpc v1.36.0
	google.golang.org/protobuf v1.26.0
	xorm.io/builder v0.3.9 // indirect
	xorm.io/core v0.7.3 // indirect
)

replace _ => ./
