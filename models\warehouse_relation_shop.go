package models

import (
	"time"
)

type WarehouseRelationShop struct {
	Id            int       `xorm:"not null pk autoincr comment('自增id') INT(11)"`
	ShopName      string    `xorm:"default 'NULL' comment('门店名称') VARCHAR(255)"`
	ShopId        string    `xorm:"not null comment('门店id') VARCHAR(255)"`
	WarehouseName string    `xorm:"default 'NULL' comment('仓库名称') VARCHAR(255)"`
	WarehouseId   int       `xorm:"not null comment('仓库id') index INT(11)"`
	CreateTime    time.Time `xorm:"not null comment('创建时间') DATETIME"`
	ChannelId     int       `xorm:"not null comment('渠道id：2 美团') INT(11)"`
}
