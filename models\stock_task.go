package models

import "time"

type StockTask struct {
	Id           int32     `xorm:"not null pk autoincr INT(11)"`
	Request      string    `xorm:"default 'NULL'  comment('请求参数') varchar(10000)"`
	SerialNumber string    `xorm:"default 'NULL'  comment('市场价') varchar(100)"`
	Status       int32     `xorm:"default NULL comment('状态 0:未处理 1：已处理') INT(11)"`
	CreateDate   time.Time `xorm:"default 'NULL' comment('创建时间') DATETIME"`
	LastDate     time.Time `xorm:"default 'NULL' comment('最后更新时间') DATETIME"`
	StockType    int32     `xorm:"default 1 comment('库存类型：1：子龙，2：OMS') INT(11)"`
	DoCount      int32     `xorm:"default 0 comment('处理次数，如果处理了3次还没成功就不处理了') INT(11)"`
}
