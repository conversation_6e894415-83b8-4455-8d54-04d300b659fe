package services

import (
	"_/proto/ic"
	"context"
	"fmt"
	"testing"
)

func TestGJPService_GetA8VirtualGoodsQtyForQZC(t *testing.T) {
	//type fields struct {
	//	DBService DBService
	//}
	//type args struct {
	//	warehouse models.Warehouse
	//	typeidStr string
	//	goodsId   []string
	//}
	//tests := []struct {
	//	name   string
	//	fields fields
	//	args   args
	//	want   bool
	//}{
	//	// TODO: Add test cases.
	//	{name: "前置仓测试",
	//		args: args{
	//			warehouse: models.Warehouse{
	//				Code: "021SHDJWLD",
	//			},
	//			typeidStr: "",
	//			goodsId:   []string{"SPKW04"},
	//		}},
	//}
	//for _, tt := range tests {
	//	t.Run(tt.name, func(t *testing.T) {
	//		s := &GJPService{
	//			DBService: tt.fields.DBService,
	//		}
	//		if got := s.GetA8VirtualGoodsQtyForQZC(tt.args.warehouse, tt.args.typeidStr, tt.args.goodsId); got != tt.want {
	//			t.Errorf("GetA8VirtualGoodsQtyForQZC() = %v, want %v", got, tt.want)
	//		}
	//	})
	//}
}

func Test_getQuanQuDaoStockInfo(t *testing.T) {
	type args struct {
		goodsCodes    string
		warehouseCode string
	}
	tests := []struct {
		name string
		args args
	}{
		{name: "单独调用全渠道数据",
			args: args{
				goodsCodes:    "BJHG004",
				warehouseCode: "010QDLSD",
			}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			getQuanQuDaoStockInfo(tt.args.goodsCodes, tt.args.warehouseCode)
		})
	}
}

func Test_IsOmsWarehouse(t *testing.T) {
	type args struct {
		goodsCodes    string
		warehouseCode string
	}
	tests := []struct {
		name string
		args args
	}{
		{name: "单独调用全渠道数据",
			args: args{
				goodsCodes:    "BJHG004",
				warehouseCode: "010QDLSD",
			}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service := new(StockService)
			vo := ic.IsOmsWarehouseVo{
				Warehouse: "abc",
			}
			resp, err := service.IsOmsWarehouseCode(context.Background(), &vo)
			fmt.Println("resp:", resp, err)
		})
	}
}

func TestStockService_IsOmsWarehouseCode(t1 *testing.T) {
	type args struct {
		ctx context.Context
		in  *ic.IsOmsWarehouseVo
	}
	tests := []struct {
		name    string
		args    args
		want    *ic.IsOmsWarehouseResp
		wantErr bool
	}{
		{
			name: "",
			args: args{in: &ic.IsOmsWarehouseVo{Warehouse: "021SHHXD"}},
		},
	}
	for _, tt := range tests {
		t1.Run(tt.name, func(t1 *testing.T) {
			t := StockService{}
			got, err := t.IsOmsWarehouseCode(tt.args.ctx, tt.args.in)
			t1.Log(got)
			if (err != nil) != tt.wantErr {
				t1.Errorf("IsOmsWarehouseCode() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}
