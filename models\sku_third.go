package models

type SkuThird struct {
	Id            int32  `xorm:"not null pk autoincr INT(11)"`
	SkuId         int32  `xorm:"not null comment('商品库SKU ID') INT(11)"`
	ThirdSkuId    string `xorm:"default NULL comment('第三方SKU ID') VARCHAR(36)"`
	ThirdSpuId    string `xorm:"default NULL comment('第三方SPU ID') VARCHAR(36)"`
	ErpId         int32  `xorm:"default NULL comment('ERP仓库ID') INT(11)"`
	ProductId     int32  `xorm:"default NULL comment('商品ID') INT(11)"`
	ThirdSpuSkuId string `xorm:"default NULL comment('拼接的货号，作唯一键') VARCHAR(73)"`
}
