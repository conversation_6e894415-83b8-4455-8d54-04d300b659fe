package utils

import (
	"github.com/limitedlee/microservice/common/config"
	logger "github.com/maybgit/glog"
	"github.com/streadway/amqp"
)

//开启一个mq链接
func NewMqConn() *amqp.Connection {
	url := config.GetString("mq.oneself")
	conn, err := amqp.Dial(url)
	if err != nil {
		logger.Error("RabbitMQ dial fail. err : ", err)
		panic(err)
	}
	return conn
}

func NewMqChannel(conn *amqp.Connection) *amqp.Channel {
	channel, err := conn.Channel()
	if err != nil {
		logger.Error("RabbitMQ Get Channel fail. err : ", err)
		panic(err)
	}
	return channel
}

func Consume(queue, key, exchange string, fun func(request amqp.Delivery) (response string, err error)) {
	conn := NewMqConn()
	if conn == nil {
		logger.Error("conn is nil")

		return
	}
	defer conn.Close()

	ch := NewMqChannel(conn)
	if ch == nil {
		logger.Error("ch is nil")
		return
	}
	defer ch.Close()

	err := ch.QueueBind(queue, key, exchange, false, nil)
	if err != nil {
		//log.Error(queue, "，", key, "，", exchange, "，", err.Error())
		return
	}

	if err := ch.Qos(1, 0, false); err != nil {
		logger.Error("Rabbitmq，", queue, "，", key, "，", exchange, "，", err.Error())
	}
	go func() {
		closeMsg := <-conn.NotifyClose(make(chan *amqp.Error))
		logger.Warningf("Rabbitmq Consume closing: %s", closeMsg)
	}()

	go func() {
		closeMsg := <-ch.NotifyClose(make(chan *amqp.Error))
		logger.Warningf("Rabbitmq Consume closing: %s", closeMsg)
	}()

	delivery, err := ch.Consume(queue, queue, false, false, false, false, nil)
	if err != nil {
		logger.Error(err)
	}

	for d := range delivery {
		func() {
			defer func() {
				if err := recover(); err != nil {
					logger.Error(err)
				}
			}()
		}()
		fun(d) // 业务处理
	}
}
