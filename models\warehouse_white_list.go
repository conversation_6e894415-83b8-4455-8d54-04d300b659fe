package models

import (
	"time"
)

type WarehouseWhiteList struct {
	Id            int       `xorm:"not null pk autoincr comment('自增id') INT(10)"`
	WarehouseCode string    `xorm:"not null default '''' comment('仓库编码') VARCHAR(12)"`
	WarehouseName string    `xorm:"not null default '''' comment('仓库名称') VARCHAR(64)"`
	CreateTime    time.Time `xorm:"default 'NULL' comment('创建时间') DATETIME created"`
	UpdateTime    time.Time `xorm:"default 'current_timestamp()' comment('更新时间') DATETIME"`
}
