package services

import (
	"database/sql"
	_ "github.com/denisenkom/go-mssqldb"
	_ "github.com/go-sql-driver/mysql"
	"github.com/go-xorm/xorm"
	"github.com/limitedlee/microservice/common/config"
	"github.com/tricobbler/mqgo"
	"os"
	"time"
)

type DBService struct {
}

//向mq推送消息
//queue：队列名-也是路由的key
//exchange：交换机名称
//content：消息内容
func PulishMq(queue, exchange, content string) (result int, err error) {
	//当mysqlConnStr等于空时，消息内容不会写入数据库
	if err := mqgo.NewMq(config.GetString("mq.oneself"), GetMysqlConn()).Publish(mqgo.SyncMqInfo{
		Exchange: exchange,
		RouteKey: queue,
		Queue:    queue,
		Request:  content,
	}); err != nil {
		return 505, err
	}
	return 200, nil
}

//单个程序获取数据库连接
func GetMysqlConn() *xorm.Engine {
	mySqlStr := config.GetString("mysql.dc_dispatch")
	engine, err := xorm.NewEngine("mysql", mySqlStr)
	if err != nil {
		panic(err)
	}

	// 开发环境显示sql日志
	if os.Getenv("ASPNETCORE_ENVIRONMENT") == "" {
		engine.ShowSQL(true)
	}

	location, err := time.LoadLocation("Asia/Shanghai")
	engine.SetTZLocation(location)

	return engine
}

//子龙库存是否跑全量数据，1是0否
func GetIsAll() bool {
	mySqlStr := config.GetString("dc_dispatchTaskIsAll")
	if mySqlStr == "1" {
		return true
	}
	return false
}

//单个程序获取数据库连接
func GetDCOrderMysqlConn() *xorm.Engine {
	mySqlStr := config.GetString("mysql.dc_order")
	engine, err := xorm.NewEngine("mysql", mySqlStr)
	if err != nil {
		panic(err)
	}

	//engine.ShowSQL()
	location, err := time.LoadLocation("Asia/Shanghai")
	engine.SetTZLocation(location)
	return engine
}

//单个程序获取数据库连接
func GetDCProductMysqlConn() *xorm.Engine {
	mySqlStr := config.GetString("mysql.dc_product")
	engine, err := xorm.NewEngine("mysql", mySqlStr)
	if err != nil {
		panic(err)
	}

	//engine.ShowSQL()
	location, err := time.LoadLocation("Asia/Shanghai")
	engine.SetTZLocation(location)
	return engine
}

//获取A8的数据库链接
func GetMssqlConn() *sql.DB {
	//mySqlStr := "server=192.168.3.13;database=rhgyl;user id=sa;password=TESTtest;port=1433;encrypt=disable"
	mySqlStr := config.GetString("MsSql_rhscm")
	db, err := sql.Open("mssql", mySqlStr)
	if err != nil {
		panic(err)
	}
	//db.Ping()
	return db
}

//单个程序获取数据库连接
func DatacenterMysqlConn() *xorm.Engine {
	mySqlStr := config.GetString("mysql.datacenter")
	engine, err := xorm.NewEngine("mysql", mySqlStr)
	if err != nil {
		panic(err)
	}

	//engine.ShowSQL()
	location, err := time.LoadLocation("Asia/Shanghai")
	engine.SetTZLocation(location)
	return engine
}
