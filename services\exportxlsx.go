package services

import (
	"_/dtos"
	"_/utils"
	"bytes"
	"errors"
	"github.com/tealeg/xlsx"
	kit "github.com/tricobbler/rp-kit"
	"os"
)

type ManualStockExport struct {
	f         *xlsx.File
	titleList []string
	sheetName string
	xlsxPath  string
	dataList  []dtos.StocksDetil
}

// 初始化 手动 单仓库存拉取 类型
func NewManualStock(dataList []dtos.StocksDetil) *ManualStockExport {
	return &ManualStockExport{
		f:         xlsx.NewFile(),
		titleList: []string{"a8货号", "商品名称", "库存数量", "仓库编码", "仓库名称"},
		sheetName: "Sheet1",
		dataList:  dataList,
	}
}

func (m *ManualStockExport) Export() error {

	// 添加sheet页
	sheet, err := m.f.AddSheet("Sheet1")
	if err != nil {
		return err
	}
	// 插入表头
	titleRow := sheet.AddRow()
	for _, v := range m.titleList {
		cell := titleRow.AddCell()
		cell.Value = v
		//表头字体颜色
		cell.GetStyle().Font.Color = "00000000"
		cell.GetStyle().Font.Bold = true
		//居中显示
		cell.GetStyle().Alignment.Horizontal = "center"
		cell.GetStyle().Alignment.Vertical = "center"
	}

	// 插入内容 导出excel
	for _, v := range m.dataList {

		row := sheet.AddRow()
		row.WriteStruct(&v, 5)
	}

	if m.xlsxPath, err = generateDownUrl(m.f); err != nil {
		return err
	}
	return nil
}

//上传至oss生成下载链接公共方法
func generateDownUrl(f *xlsx.File) (url string, err error) {
	//将数据存入buff中
	var buff bytes.Buffer
	if err = f.Write(&buff); err != nil {
		return "", errors.New("excel写入buffer失败, " + err.Error())
	}

	var name = kit.GetGuid36() + ".xlsx"
	//生成文件
	fd, err := os.Create(name)
	if err != nil {
		return "", errors.New("文件创建失败, " + err.Error())
	}

	defer os.Remove(name)
	defer fd.Close()

	if err = f.Write(fd); err != nil {
		return "", errors.New("excel写入文件失败, " + err.Error())
	}

	//同步文件到七牛云
	url, err = utils.UploadExcelToQiNiu(name)
	if err != nil {
		return "", errors.New("文件上传失败, " + err.Error())
	}

	return url, nil
}
