package services

import (
	"_/dtos"
	"_/models"
	"_/proto/ic"
	"_/proto/pc"
	"_/utils"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/streadway/amqp"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/go-redis/redis"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

type DSStockConfig struct {
	Value           string
	LastRefreshTime time.Time
}

// DSStock 5分钟刷新一次
var DSStock = DSStockConfig{
	Value:           config.GetString("DSStock"),
	LastRefreshTime: time.Now(),
}

type StockService struct {
}

// 手动 拉取单仓库存 的数据记录列表
func (c *StockService) ManualStockRecordList(ctx context.Context, request *ic.ManualStockRecordListRequest) (*ic.ManualStockRecordListResponse, error) {
	logStr := "activity-center-ManualStockRecordList-eva-入参=" + kit.JsonEncode(request)
	glog.Info(logStr)
	var (
		pageIndex         = 1
		pageSize          = 20
		err               error
		manualStockRecord []*models.ManualStockRecord
		response          = new(ic.ManualStockRecordListResponse)
	)
	response.Data = make([]*ic.ManualStockRecord, 0)

	if request.PageIndex > 0 {
		pageIndex = int(request.PageIndex)
	}
	if request.PageSize > 0 {
		pageSize = int(request.PageSize)
	}
	offset := (pageIndex - 1) * pageSize
	db := GetMysqlConn()

	session := db.NewSession()
	defer session.Close()

	sessionClone := session.Clone()
	defer sessionClone.Close()
	//如果有传时间， 则判断时间格式是否正确
	if len(request.CreateTime) > 0 {
		createTime, err := time.ParseInLocation(kit.DATETIME_LAYOUT, request.CreateTime, time.Local)
		if err != nil {
			return nil, errors.New("搜索条件，创建时间格式错误")
		}
		session.Where("create_time >= ?", createTime.Format(kit.DATETIME_LAYOUT))
	}

	if err = session.OrderBy("id desc").Limit(pageSize, offset).Find(&manualStockRecord); err != nil {
		glog.Error(logStr, ",错误-", err.Error())
		return nil, err
	}

	total, err := sessionClone.Table("manual_stock_record").Count()
	if err != nil {
		glog.Error(logStr, ",错误-", err.Error())
		return nil, err
	}
	if total == 0 {
		return response, nil
	}
	response.Total = int32(total)
	if err = utils.MapTo(manualStockRecord, &response.Data); err != nil {
		glog.Error(logStr, ",MapTo=", err.Error())
		return nil, err
	}
	return response, nil

}

func init() {
	go SubscribeRabbitMQ("dc_sz_stock_update", "ordercenter")
}

//手动触发跑redis数据
func (t StockService) RunWarehouseRedisByManual(ctx context.Context, in *ic.OmsSyncSotckResponse) (*ic.OmsSyncSotckResponse, error) {
	out := &ic.OmsSyncSotckResponse{
		Code: http.StatusBadRequest,
	}

	out.Code = 200
	return out, nil
}

//手动触发跑前置仓任务
func (t StockService) RunStockByManual(ctx context.Context, in *ic.RunStockByManualRequest) (*ic.OmsSyncSotckResponse, error) {
	// 已切oms
	return nil, nil
}

//todo 2021年3月8日 库存冻结逻辑
//todo redis实时库存直接减去冻结库存，在途库存不用管,订单中心会写进去，反向流程，需要删除在途库存，把删除的数量加回到redis的实时库存
func (t StockService) FreezeStock(ctx context.Context, in *ic.FreezeRequest) (*ic.FreezeResponse, error) {
	glog.Info("冻结商品库存请求参数：", in)
	var out ic.FreezeResponse
	redisConn := utils.ConnectClusterRedis()
	defer redisConn.Close()
	freezeStockKey := "s2b2c:stock:freeze:" + in.OrderId
	if len(in.OrderId) > 0 {
		result := redisConn.SetNX(freezeStockKey, time.Now().Unix(), 15*time.Minute)
		if !result.Val() {
			out.Message = "请不要重复提交订单"
			out.Code = 400
			return &out, nil
		}
	} else {
		out.Message = "请求订单号不能为空"
		out.Code = 400
		return &out, nil
	}
	var paramsStrArr []string
	for _, v := range in.GoodsList {
		paramsStr := fmt.Sprintf("stock:%s-%d", v.GoodsId, v.Number)
		paramsStrArr = append(paramsStrArr, paramsStr)
	}
	//如果来源是电商，需要去配置中心取仓库id
	WarehouseId := ""
	if in.Source == 1 {
		//todo 电商库存冻结的是物理仓-不存在逻辑仓。
		WarehouseId = "DSC001"
	} else if in.Source == 2 || in.Source == 3 {
		if len(in.GoodsList) <= 0 {
			out.Message = "仓库id不能为空"
			out.Code = 400
			return &out, nil
		}
		WarehouseId = cast.ToString(in.GoodsList[0].WarehouseId)
	} else {
		out.Message = "来源信息不合法"
		out.Code = 400
		return &out, nil
	}

	// 确保库存缓存数据必须存在，否则执行lua脚本会数据不正确或报库存不足
	t.makeSureStockCache(redisConn, WarehouseId, in.GoodsList)

	scriptStr := `
	local delimiter = "-"
 	local skuArray = {} 
 	local ckArray = {} 
 	local stockArray = {} 
 	local redisStockArray = {} 
 	local warehouseId = ARGV[2]
	local noStockStr = ""
	local skuStr = {}
	for i= 1, ARGV[1] do
		local sourceStr = KEYS[i]
		local findStart,findEnd = string.find(KEYS[i],delimiter)
		local stock_key = string.sub(sourceStr, 1, findStart-1) 
		skuArray[i]=stock_key
		local stock_value = string.sub(sourceStr, findStart+1, string.len(KEYS[i]))
		stockArray[i]=stock_value
		-- 查询redis库存信息
		local stock_redis_value = tonumber(redis.call('HGET', stock_key, warehouseId))
		if stock_redis_value == nil then 
			skuStr[1] = stock_key
			return skuStr
		end
		redisStockArray[i]=stock_redis_value
		-- 判断库存是否满足
		if(stock_redis_value-stock_value >= 0) then 
			if (stock_redis_value-stock_value==0) then 
				noStockStr = noStockStr .. "," .. stock_key
			end
			redisStockArray[i]=stock_redis_value
		else
			-- 如果不满足，则直接return出去
			skuStr[1] = stock_key
			return skuStr
		end 
	 end
	 -- 验证通过后去redis操作数据
	 for i= 1, ARGV[1] do
		-- 查询redis库存信息
		local stock_redis_value = tonumber(redis.call('HGET', skuArray[i], warehouseId))
		local stock_value=tonumber(stockArray[i])
		-- 扣减库存
		local stock_num=stock_redis_value-stock_value
		-- 扣减完成后重新设置redis
		redis.call('HSET',skuArray[i],warehouseId,stock_num)
	 end
	 return "1|" .. noStockStr
	`
	//处理lua参数格式
	result := redisConn.Eval(scriptStr, paramsStrArr, len(paramsStrArr), WarehouseId)
	resultType := fmt.Sprintf("%T", result.Val())

	//处理完成后删除锁
	redisConn.Del(freezeStockKey)
	//if resultType == "int64" {
	//	out.Code = 200
	//	out.Message = "success"
	//	return &out, nil
	//} else
	if resultType == "string" {
		resultInterface := result.Val().(interface{})
		sptStr := resultInterface.(string)
		//"1|,stock:skuid,stock:skuid,stock:skuid" 或者 "1|"
		noStockArry := strings.Split(sptStr, "|")
		if len(noStockArry) > 0 {
			if noStockArry[0] == "1" {
				if in.Source == 2 {
					//本地生活处理库存冻结到0的逻辑。
					go dealProductStockNoticeParam(WarehouseId, noStockArry[1], 0)
				}
				out.Code = 200
				out.Message = "success"
				return &out, nil
			}
		}
		out.Message = "商品冻结失败"
		out.Code = 400
		return &out, nil
	} else if resultType == "<nil>" {
		out.Message = "查询库存失败，lua脚本有报错，错误内容：" + result.Err().Error()
		out.Code = 400
		return &out, nil
	} else {
		resultInterface := result.Val().([]interface{})
		sptStr := resultInterface[0].(string)
		resultMsg := strings.Split(sptStr, ":")
		out.Message = "商品" + resultMsg[1] + "库存不足"
		out.Code = 400
		return &out, nil
	}
}

// FreedStock 根据条件释放库存
//订单发生取消或者退货等逻辑时，订单中心删除在途库存，并把库存加回来。
func (t StockService) FreedStock(ctx context.Context, in *ic.FreedStockRequest) (*ic.FreedStockResponse, error) {
	glog.Info("解冻商品库存请求参数：", in)
	var out ic.FreedStockResponse
	if len(in.OrderId) <= 0 {
		out.Code = 400
		out.Message = "订单号不能为空"
		return &out, nil
	}
	out.Code = 200
	out.Message = fmt.Sprintf("订单号：%s", in.OrderId)
	redisConn := utils.ConnectClusterRedis()
	defer redisConn.Close()
	if len(in.OrderId) > 0 {
		result := redisConn.SetNX("s2b2c:stock:freed:"+in.OrderId, time.Now().Unix(), 15*time.Minute)
		if !result.Val() {
			out.Message = "请不要重复提交订单"
			out.Code = 400
			return &out, nil
		}
	} else {
		out.Message = "请求订单号不能为空"
		out.Code = 400
		return &out, nil
	}
	var paramsStrArr []string
	var stockSkuIdArr []string
	for _, v := range in.GoodsList {
		paramsStr := fmt.Sprintf("stock:%s-%d", v.GoodsId, v.Number)
		paramsStrArr = append(paramsStrArr, paramsStr)
		stockSkuIdArr = append(stockSkuIdArr, fmt.Sprintf("stock:%s", v.GoodsId))
	}

	//如果来源是电商，需要去配置中心取仓库id
	WarehouseId := ""
	if in.Source == 1 {
		//todo 电商库存冻结的是物理仓-不存在逻辑仓。
		WarehouseId = "DSC001"
	} else {
		if len(in.GoodsList) <= 0 {
			out.Message = "没有释放库存的商品信息"
			out.Code = 400
			return &out, nil
		}
		WarehouseId = cast.ToString(in.GoodsList[0].WarehouseId)
	}
	if WarehouseId == "" {
		out.Message = "仓库id不能为空"
		out.Code = 400
		return &out, nil
	}

	// 确保库存缓存数据必须存在，否则执行lua脚本会数据不正确或报库存不足
	t.makeSureStockCache(redisConn, WarehouseId, in.GoodsList)

	scriptStr := `
	local delimiter = "-"
 	local warehouseId = ARGV[2]
	for i= 1, ARGV[1] do
		local sourceStr = KEYS[i]
		local findStart,findEnd = string.find(KEYS[i],delimiter)
		-- skuid
		local stock_key = string.sub(sourceStr, 1, findStart-1)
		-- number 数量
		local stock_value = string.sub(sourceStr, findStart+1, string.len(KEYS[i]))
		stock_value=tonumber(stock_value)

		-- 查询是否存在，存在直接加回，不存在，新设置
		local exists=redis.call('HExists',stock_key,warehouseId)
		 if exists==1 then
			-- 查询redis库存信息
			local stock_redis_value = tonumber(redis.call('HGET', stock_key, warehouseId))  
			stock_redis_value=tonumber(stock_redis_value)
			-- 计算加回的值
			stock_redis_value=stock_redis_value+stock_value
			-- 重新设置redis，加回库存
			redis.call('HSET',stock_key,warehouseId,stock_redis_value)
		else
			redis.call('HSET',stock_key,warehouseId,stock_value)
		end
	 end
	 return 1
	`
	//处理lua参数格式
	result := redisConn.Eval(scriptStr, paramsStrArr, len(paramsStrArr), WarehouseId)
	resultType := fmt.Sprintf("%T", result.Val())
	//处理完成后删除锁
	redisConn.Del(in.OrderId)
	if resultType == "int64" {
		if in.Source == 2 {
			//本地生活处理库存释放库存。
			go dealProductStockNoticeParam(WarehouseId, strings.Join(stockSkuIdArr, ","), 1)
		}

		out.Code = 200
		out.Message = "success"
		return &out, nil
	} else {
		if result.Val() == nil {
			out.Message = "释放库存失败，result.Val() is nil"
		} else {
			resultInterface := result.Val().([]interface{})
			sptStr := resultInterface[0].(string)
			resultMsg := strings.Split(sptStr, ":")
			out.Message = "释放库存失败" + resultMsg[1]
		}
		out.Code = 400
		return &out, nil
	}
}

// 确保库存缓存数据必须存在，不存在则通过查询缓存方式重新加入缓存中
func (t StockService) makeSureStockCache(conn *redis.Client, warehouseId string, list []*ic.OrderGoodsInfo) {
	if len(list) == 0 {
		return
	}
	pipeline := conn.Pipeline()
	defer pipeline.Close()

	for _, v := range list {
		pipeline.HExists(fmt.Sprintf("stock:%s", v.GoodsId), warehouseId)
	}

	result, err := pipeline.Exec()
	if err != nil {
		glog.Errorf("makeSureStockCache 批量查询库存缓存异常:%+v %s %s", err, kit.JsonEncode(list), warehouseId)
		return
	}

	var nonCacheList []*ic.OrderGoodsInfo
	for i, v := range result {
		cmd := v.(*redis.BoolCmd)
		if cmd.Val() {
			continue
		}
		// 库存缓存不存在则
		nonCacheList = append(nonCacheList, list[i])
	}

	if len(nonCacheList) == 0 {
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*30)
	defer cancel()
	req := &ic.GetStockInfoRequest{
		Source:     2,
		IsNeedPull: 0,
		Stockwarehouse: []int32{
			cast.ToInt32(warehouseId),
		},
	}
	for _, v := range nonCacheList {
		req.ProductsInfo = append(req.ProductsInfo, &ic.ProductsInfo{
			Type:  2,
			SkuId: cast.ToInt32(v.GoodsId),
		})
	}
	resp, err := t.GetStockInfo(ctx, req)
	if err != nil {
		glog.Errorf("makeSureStockCache 批量查询库存信息异常:%+v %s", err, kit.JsonEncode(req))
	}
	glog.Infof("makeSureStockCache 批量查询库存信息结果:%+v 请求参数:%s", kit.JsonEncode(resp), kit.JsonEncode(req))
}

// SubscribeRabbitMQ 订阅mq消息
func SubscribeRabbitMQ(queue, exchange string) {
	defer func() {
		if err := recover(); err != nil {
			glog.Error("库存中心订阅数据失败(SubscribeRabbitMQ)：", err)
		}
	}()

	utils.Consume(queue, queue, exchange,
		func(d amqp.Delivery) (response string, err error) {
			var notices []string
			defer func() {
				d.Ack(false)
				glog.Info(fmt.Sprintf("订阅队列为%s的信息内容：%s，返回%s,%v,%v", queue, d.Body, response, err, notices))
			}()

			glog.Info("dc_sz_stock_update 获取到数据：" + string(d.Body))
			//解析内容
			var updateGoodsStock dtos.UpdateGoodsStock
			if err := json.Unmarshal(d.Body, &updateGoodsStock); err != nil {
				glog.Error("dc_sz_stock_update 解析数据错误", string(d.Body), err)
				return "success", nil
			}
			//如果是OMS推送过来的库存更新数据
			if updateGoodsStock.IsOMS == 1 {
				conn := GetMysqlConn()
				defer conn.Close()

				//初始化数据库
				connProduct := GetDCProductMysqlConn()
				defer connProduct.Close()
				//循环加入库存信息
				for _, warehouse := range updateGoodsStock.Data {
					//查询仓库信息
					warehouseMode := models.Warehouse{}
					_, _ = conn.Where("code=?", warehouse.WarehouseCode).And("status=1").Get(&warehouseMode)
					var mapGoodsQty []dtos.GoodsStock
					var goodsIds []string
					//循环加入库存信息 , todo 后续增加多仓情况，需要走配置文件处理。
					for _, stockDetail := range warehouse.Goodslist {
						//如果是电商仓，并且商品标识为药品的数据直接过滤掉，不处理
						//药品仓必须是打上药品标识的数据，非药品仓不能包含有药品标识的数据
						isDrug := 0 //记录是否是药品
						if "JD01" == warehouse.WarehouseCode {
							_, err := connProduct.SQL("select count(1) from dc_product.product where id=? and warehouse_type=1", stockDetail.Spu).Get(&isDrug)
							if err != nil {
								glog.Info("查询是否药品仓出错：" + err.Error())
							}
							if isDrug > 0 {
								continue
							}
						} else if "JXYQC01" == warehouse.WarehouseCode {
							_, err := connProduct.SQL("select count(1) from dc_product.product where id=? and warehouse_type=1", stockDetail.Spu).Get(&isDrug)
							if err != nil {
								glog.Info("查询是否药品仓出错：" + err.Error())
							}
							if isDrug == 0 {
								continue
							}
							//如果是医疗互联网的商品
						} else if "MD01" == warehouse.WarehouseCode {

						}
						//库存为零则初始化数据
						if stockDetail.Stock < 0 {
							stockDetail.Stock = 0
						}
						var _mapQty = dtos.GoodsStock{
							GoodsId: stockDetail.Sku,
							Stock:   stockDetail.Stock,
							SpuId:   stockDetail.Spu,
						}
						mapGoodsQty = append(mapGoodsQty, _mapQty)
						goodsIds = append(goodsIds, stockDetail.Sku)
					}
					writeStock(warehouseMode, goodsIds, mapGoodsQty)
					if len(goodsIds) > 0 {
						model := models.MqInfo{
							Exchange: "ordercenter",
							Quene:    "dc_sz_stock_update_to_sc",
							Content:  fmt.Sprintf("{\"goodsids\":\"%s\"}", strings.Join(goodsIds, ",")),
						}
						dcOrderEngine := GetDCOrderMysqlConn()
						defer dcOrderEngine.Close()
						_, err := dcOrderEngine.Insert(model)
						if err != nil {
							glog.Error("插入通知电商更新库存的MQ信息失败：", err)
							return "success", nil
						}
					}
				}
				return "success", nil
			}
			// 错误信息抛出不再处理
			if len(updateGoodsStock.Goodslist) == 0 {
				return "success", nil
			} else {
				if len(updateGoodsStock.Goodslist[0].Goodsid) == 0 {
					return "success", nil
				}
			}
			// 如果是瑞鹏oms
			if updateGoodsStock.IsRPOms {
				// 初始化数据库
				db := GetDCProductMysqlConn()
				defer db.Close()
				// 查询仓库信息
				var warehouse models.Warehouse
				if has, err := db.Table("dc_dispatch.warehouse").Where("code = ? AND category in (4 , 5)", updateGoodsStock.Code).
					Get(&warehouse); err != nil {
					glog.Error("查询仓库出错 " + err.Error())
					return "success", nil
					//return "fail", errors.New("查询仓库出错 " + err.Error())
				} else if !has {
					notices = append(notices, "未找到仓库")
					return "success", nil
				}

				var skuIds []string
				for _, v := range updateGoodsStock.Goodslist {
					skuIds = append(skuIds, v.Goodsid)
				}

				var stocks []dtos.GoodsStock
				if err = db.Table("dc_product.sku").Select("id as goods_id,product_id as spu_id,0 as stock").
					In("id", skuIds).Find(&stocks); err != nil {
					glog.Error("库存查询sku报错：", err)
					return "success", nil
				}

				// 增量写入库存
				if err = writeStock(warehouse, nil, stocks, true); err != nil {
					return "success", nil
				}
				return "success", nil
			}
			glog.Info("dc_sz_stock_update5 获取到数据：" + string(d.Body))
			if updateGoodsStock.Source == 3 {
				return "success", nil
			}

			//根据解析的数据处理业务逻辑
			var warehouse = models.Warehouse{}

			var mysqlEngine = GetMysqlConn()
			defer mysqlEngine.Close()

			if _, err = mysqlEngine.Where("category in (4,5) and status=1").Where("code=?", updateGoodsStock.Code).Get(&warehouse); err != nil {
				glog.Error("查询仓库信息报错：", err)
				return "success", nil
			}

			var strGoodsId []string
			mapGoodsId := make(map[string]string, 0)

			//循环处理商品明细数据
			for _, v := range updateGoodsStock.Goodslist {
				strGoodsId = append(strGoodsId, v.Goodsid)
				mapGoodsId[v.Goodsid] = v.Goodsid
			}
			//有商品的时候处理业务逻辑
			if len(strGoodsId) > 0 {
				//todo 新的迭代就不存在释放库存的逻辑了
				//获取实时库存
				if warehouse.Comefrom == 1 {
					service := GJPService{}
					if warehouse.Category == 4 || warehouse.Category == 5 {
						//todo 清除当前商品redis库存
						service.GetA8VirtualGoodsQtyForQZC(warehouse, "", strGoodsId)
						//删除redis
						redisConn := utils.ConnectClusterRedis()
						defer redisConn.Close()
						scriptStr := ` local k=""
											local warehouseId=ARGV[2]
											for i=1,ARGV[1] do 
												k=KEYS[i]
												-- 拼接redis key
												local redis_key=string.format("%s:%s",'stock',k)
												-- 删除redis 
												redis.call('HDel', redis_key, warehouseId)
											end
										  return 1
										  `
						redisConn.Eval(scriptStr, strGoodsId, len(strGoodsId), warehouse.Id)
					}
				}

				//model := models.MqInfo{
				//	Exchange: "ordercenter",
				//	Quene:    "dc_sz_stock_update_to_sc",
				//	Content:  fmt.Sprintf("{\"goodsids\":\"%s\"}", strings.Join(strGoodsId, ",")),
				//}
				//dcOrderEngine := GetDCOrderMysqlConn()
				//defer dcOrderEngine.Close()
				//_, err := dcOrderEngine.Insert(model)
				//if err != nil {
				//	glog.Error("插入通知电商更新库存的MQ信息失败：", err)
				//	return "fail", err
				//}
				//应答mq信息
				return "success", nil
			}
			glog.Info("dc_sz_stock_update6 获取到数据：" + string(d.Body))
			//成功后会ack
			return "success", nil
		})
}

// ImportThirdStock 导入第三方库存
func (c *StockService) ImportThirdStock(ctx context.Context, in *ic.ThirdInfoRequest) (*ic.ThirdInfoResponse, error) {
	out := new(ic.ThirdInfoResponse)
	out.Code = 400
	var mapGoodsQty []dtos.GoodsStock
	for _, v := range in.Details {
		mapGoodsQty = append(mapGoodsQty, dtos.GoodsStock{
			GoodsId: v.ThirdSpuSkuId,
			Stock:   int(v.Stock),
		})
	}
	writeStock(models.Warehouse{Id: int(in.WarehouseId)}, []string{}, mapGoodsQty)
	out.Code = 200
	return out, nil
}
func (c *StockService) InventoryTaskCall(ctx context.Context, in *ic.TaskCommonRequest) (*ic.TaskCommonResponse, error) {
	resp := new(ic.TaskCommonResponse)
	resp.Code = 200

	wg := &sync.WaitGroup{}
	switch in.Type {
	case 1:
		wg.Add(1)
		go func(wg *sync.WaitGroup) {
			SyncA8VirtualGoodsQtyForQZC()
			wg.Done()
		}(wg)
	case 2:
		wg.Add(1)
		go func(wg *sync.WaitGroup) {
			SyncA8VirtualGoodsQtyForQZCVirtual()
			wg.Done()
		}(wg)
	case 3:
		wg.Add(1)
		go func(wg *sync.WaitGroup) {
			ToProvideSyncStock()
			wg.Done()
		}(wg)
	default:
		wg.Add(3)
		go func(wg *sync.WaitGroup) {
			SyncA8VirtualGoodsQtyForQZC()
			wg.Done()
		}(wg)
		go func(wg *sync.WaitGroup) {
			SyncA8VirtualGoodsQtyForQZCVirtual()
			wg.Done()
		}(wg)
		go func(wg *sync.WaitGroup) {
			ToProvideSyncStock()
			wg.Done()
		}(wg)
	}

	wg.Wait()

	return resp, nil
}

// GetStockInfo 根据商品id和库存id获取库存信息
func (c *StockService) GetStockInfo(ctx context.Context, in *ic.GetStockInfoRequest) (*ic.GetStockInfoResponse, error) {
	glog.Info("查询库存的参数：", in)
	var out ic.GetStockInfoResponse
	if len(in.ProductsInfo) <= 0 {
		out.Code = 400
		out.Message = "请求参数不能为空"
		return &out, nil
	}

	// 只要不是电商仓库ID必须传，不然不知道要查询哪个渠道的库存
	if in.Source != 1 && len(in.Stockwarehouse) == 0 {
		out.Code = 400
		out.Message = "仓库ID不能为空"
		return &out, nil
	}

	var financeCode string
	//查询财务编码对应的仓库id
	//查询数据库=
	//var dcDispatchConn = GetMysqlConn()
	var warehouseList []models.Warehouse
	warehouseMap := make(map[string]models.Warehouse)
	warehouseMapId := make(map[string]models.Warehouse)
	//todo 找出所有的实物商品，去数据库查询，找出所有的实物商品，去redis查询，循环结果，如果redis没有，则把数据库查询的结果填充进去，如果有，直接使用redis的结果
	//获取传参进来的所有的实物skuid集合
	var goodsIdList []string
	//获取传参进来的所有的仓库id
	var warehouseIdList []string
	//仓库id变量--电商专用
	warehouseId := "DSC001"
	//得到所有的财务编码
	for _, v := range in.ProductsInfo {
		////本地生活查询仓库
		//if financeCode == "" && len(v.FinanceCode) > 0 {
		//	financeCode = v.FinanceCode[0]
		//	out.Code = 400
		//	out.Message = "v6.18新版不能按财务编码查询，不知道查的哪个渠道的"
		//	return &out, nil
		//}
		if v.Type == 1 { //组合
			for _, c := range v.ChildRen {
				if c.IsVirtual == 0 {
					goodsIdList = append(goodsIdList, fmt.Sprintf("'%d'", c.SkuId))
				}
			}
		} else { //非组合
			goodsIdList = append(goodsIdList, fmt.Sprintf("'%d'", v.SkuId))
		}
	}
	//如果sku集合不存在实物商品，则不用查库了，直接返回结果，库存为0
	if len(goodsIdList) <= 0 {
		out.Code = 200
		out.GoodsInfo = in
		return &out, nil
	}

	redisConn := utils.GetRedisConn()
	//defer redisConn.Close()
	if in.Source == 2 { //本地生活的
		var myWarehouse []models.Warehouse

		//如果没有传仓库，那么就按财务编码有多少个查多少
		if len(in.Stockwarehouse) == 0 {
			for _, model := range myWarehouse {
				//启用才
				if model.Status == 1 {
					warehouseList = append(warehouseList, model)
				}
			}
		} else if financeCode == "" {
			// 如果指定仓库但没有指定财务编码则需要根据仓库id查询仓库详情
			func() {
				warehouseDb := GetMysqlConn()
				defer warehouseDb.Close()
				err := warehouseDb.In("id", in.Stockwarehouse).Find(&warehouseList)
				if err != nil {
					glog.Errorf("GetStockInfo 根据仓库ID查询详情异常:%+v %s", err, kit.JsonEncode(in))
					return
				}
			}()

		} else {
			//如果指定了仓库，那就按指定的查询
			for _, model := range myWarehouse {
				for _, id := range in.Stockwarehouse {
					if int(id) == model.Id {
						warehouseList = append(warehouseList, model)
						break
					}
				}
			}
		}

		for _, v := range warehouseList {
			warehouseMap[v.Code] = v
			warehouseMapId[strconv.Itoa(v.Id)] = v
			warehouseIdList = append(warehouseIdList, strconv.Itoa(v.Id))
			//break
		}
	} else if in.Source == 1 { //电商的
		dsStock := config.GetString("DSStock")
		dsStockArr := strings.Split(dsStock, ",")
		if len(dsStockArr) > 0 {
			for _, v := range dsStockArr {
				warehouseIdList = append(warehouseIdList, v)
			}
		}
	} else { //医疗互联网的
		for _, v := range in.Stockwarehouse {
			warehouseIdList = append(warehouseIdList, cast.ToString(v))
		}
		warehouseId = cast.ToString(in.Stockwarehouse[0])
	}

	//查询数据库 -- 总库存数量 = 数据库中的库存(warehouse_goods) - 在途库存(order_freeze_stock)
	var mysqlEngine = utils.NewDbConn()
	var warehouseGoodsList []models.WarehouseGoods

	goodsIdListStr := strings.Join(goodsIdList, ",")
	warehouseIdListStr := strings.Join(warehouseIdList, ",")

	if in.Source != 1 {
		sql := "SELECT  a.goodsid, (IFNULL(SUM(DISTINCT a.stock),0)-IFNULL(SUM(b.stock),0)) as stock,a.`warehouse_id`  FROM dc_order.warehouse_goods a  " +
			" left JOIN dc_order.order_freeze_stock b ON a.warehouse_id = b.warehouse_id AND a.goodsid = b.sku_id " +
			"  WHERE  a.warehouse_id IN(%s) " +
			" and    a.`goodsid` in (%s) " +
			" GROUP BY a.goodsid,a.warehouse_id;"
		mysqlEngine.SQL(fmt.Sprintf(sql, warehouseIdListStr, goodsIdListStr)).Find(&warehouseGoodsList)
	} else {
		sql := "SELECT  a.goodsid, (IFNULL(SUM(DISTINCT a.stock),0)-IFNULL(SUM(b.stock),0)) as stock,a.`warehouse_id`  FROM dc_order.warehouse_goods a  " +
			" left JOIN dc_order.order_freeze_stock b ON  (a.warehouse_id = b.warehouse_id OR b.warehouse_id = 0) AND a.goodsid = b.sku_id " +
			"  WHERE  a.warehouse_id IN(%s) " +
			" and    a.`goodsid` in (%s) " +
			" GROUP BY a.goodsid,a.warehouse_id;"
		mysqlEngine.SQL(fmt.Sprintf(sql, warehouseIdListStr, goodsIdListStr)).Find(&warehouseGoodsList)
	}

	//设置仓库的id
	var warehouseIdStr []string
	//var warehouseIdInt []int
	if in.Source == 1 {
		//warehouseIdStr=warehouseId
		warehouseIdStr = append(warehouseIdStr, warehouseId)
	} else if in.Source == 2 {
		//本地生活 - 目前一点对一仓库逻辑
		for _, v := range warehouseMap {
			warehouseIdStr = append(warehouseIdStr, cast.ToString(v.Id))
			//warehouseIdInt = append(warehouseIdInt, v.Id)
		}
	} else { //医疗互联网
		for _, v := range in.Stockwarehouse {
			warehouseIdStr = append(warehouseIdStr, cast.ToString(v))
		}
	}

	//定义sku-warehouse map
	skuWarehouseMap := make(map[string]models.WarehouseGoods)
	if len(warehouseGoodsList) == 0 {
		for _, wid := range warehouseIdStr {

			for _, v := range in.ProductsInfo {
				warehouseGoods := models.WarehouseGoods{
					Goodsid:     strconv.Itoa(int(v.SkuId)),
					Stock:       0,
					WarehouseId: cast.ToInt(wid),
				}
				if v.Type == 1 {
					for _, cr := range v.ChildRen {
						if cr.IsVirtual == 0 {
							skuWarehouseMap[fmt.Sprintf("%d-%s", cr.SkuId, wid)] = warehouseGoods
						}
					}
				} else {
					skuWarehouseMap[fmt.Sprintf("%d-%s", v.SkuId, wid)] = warehouseGoods
				}
			}

		}

	} else {
		//这里需要判断，如果是来源是电商，那只有一个总仓库对应的库存
		for _, v := range warehouseGoodsList {
			//这里需要判断是否包含电商仓库的逻辑仓库id,包含，则是电商仓，不包含则是本地生活仓
			if in.Source == 1 || in.Source == 3 {
				mapKey := fmt.Sprintf("%s-%s", v.Goodsid, warehouseId)
				if _, ok := skuWarehouseMap[mapKey]; ok {
					_skuWarehouse := skuWarehouseMap[mapKey]
					_skuWarehouse.Stock += v.Stock
					skuWarehouseMap[mapKey] = _skuWarehouse
				} else {
					skuWarehouseMap[mapKey] = v
				}
			} else {
				//本地生活
				skuWarehouseMap[fmt.Sprintf("%s-%d", v.Goodsid, v.WarehouseId)] = v
			}
		}
	}
	//如果无法获取到商品库存信息
	if len(skuWarehouseMap) > 0 {
		for i := 0; i < len(in.ProductsInfo); i++ {
			for _, wid := range warehouseIdStr {
				v := in.ProductsInfo[i]
				//如果是组合
				if v.Type == 1 {
					var calNum []int
					for _, cr := range v.ChildRen {
						if cr.IsVirtual == 1 {
							//todo 如果是虚拟，则直接跳过，不用计算
							continue
						}
						//从redis获取库存
						redisStockStr := redisConn.HGet(fmt.Sprintf("stock:%d", cr.SkuId), wid).Val()
						redisStock := cast.ToInt32(redisStockStr)
						//如果redis不存在，则去查库

						if !redisConn.HExists(fmt.Sprintf("stock:%d", cr.SkuId), wid).Val() {
							mysqlStock := skuWarehouseMap[fmt.Sprintf("%d-%s", cr.SkuId, wid)]

							//todo 查库并且写到redis
							if in.Source == 2 {
								//todo 目前门店仓只有一个仓库
								warehouse := warehouseMapId[wid]

								if in.IsNeedPull == 1 && warehouse.Category == 3 {

									mysqlStock = pullZiLongStock(redisConn, warehouse, cr.SkuId)

								}
							}

							// 允许负库存，将查询到的库存缓存到redis中
							redisStock = cast.ToInt32(mysqlStock.Stock)
							redisConn.HSet(fmt.Sprintf("stock:%d", cr.SkuId), wid, redisStock)
						}
						if redisStock > 0 {
							calRedis := redisStock / cr.RuleNum
							calNum = append(calNum, cast.ToInt(calRedis))
						} else {
							calNum = append(calNum, 0)
						}
					}
					if len(calNum) > 0 {
						sort.Ints(calNum)
						in.ProductsInfo[i].Stock = in.ProductsInfo[i].Stock + cast.ToInt32(calNum[0])
					} else {
						calNum = append(calNum, 0)
					}
					if in.Source == 2 {
						//加入按仓库返回的列表数据
						productWarhouse := ic.ProductsInWarehouse{}
						productWarhouse.SkuId = in.ProductsInfo[i].SkuId
						productWarhouse.Stock = cast.ToInt32(calNum[0])
						productWarhouse.WarehouseId = cast.ToInt32(wid)
						out.GoodsInWarehouse = append(out.GoodsInWarehouse, &productWarhouse)

						param := pc.StockNoticeRequest{}
						//按仓库推送组合商品的有无库存
						stockStore := &pc.StockStore{
							FinanceCode: "#" + wid,
						}
						storeSku := &pc.StoreSku{
							SkuId: in.ProductsInfo[i].SkuId,
							Stock: cast.ToInt32(calNum[0]),
						}
						stockStore.Skus = append(stockStore.Skus, storeSku)
						param.StockInfo = append(param.StockInfo, stockStore)
						//组合商品查询库存二次通知
						go groupProductStockNotice(param)
					}
				} else {
					//不是组合
					redisStockStr := redisConn.HGet(fmt.Sprintf("stock:%d", v.SkuId), wid).Val()
					redisStock, _ := strconv.Atoi(redisStockStr)
					if !redisConn.HExists(fmt.Sprintf("stock:%d", v.SkuId), wid).Val() {
						//todo 查库并且写到redis
						mysqlStock := skuWarehouseMap[fmt.Sprintf("%d-%s", v.SkuId, wid)]
						if in.Source == 2 {
							//todo 目前门店仓只有一个仓库
							warehouse := warehouseMapId[wid]
							if in.IsNeedPull == 1 && warehouse.Category == 3 {
								mysqlStock = pullZiLongStock(redisConn, warehouse, v.SkuId)
							}
						}

						// 允许负库存，将查询到的库存缓存到redis中
						redisStock = mysqlStock.Stock
						redisConn.HSet(fmt.Sprintf("stock:%d", v.SkuId), wid, redisStock)
					}
					//加入按仓库返回的列表数据
					productWarhouse := ic.ProductsInWarehouse{}
					productWarhouse.SkuId = in.ProductsInfo[i].SkuId
					productWarhouse.Stock = cast.ToInt32(redisStock)
					productWarhouse.WarehouseId = cast.ToInt32(wid)
					out.GoodsInWarehouse = append(out.GoodsInWarehouse, &productWarhouse)
					//多个仓库的库存加起来返回
					in.ProductsInfo[i].Stock = in.ProductsInfo[i].Stock + cast.ToInt32(redisStock)
				}
			}
		}
		out.Code = 200
		out.GoodsInfo = in
		return &out, nil
	} else {
		out.Code = 200
		out.GoodsInfo = in
		out.Message = "查无数据，请检查后重试"
		return &out, nil
	}
}

// QueryStock 查库存，兼容组合商品
func (c *StockService) QueryStock(ctx context.Context, in *ic.QueryStockReq) (out *ic.QueryStockRes, e error) {
	out = &ic.QueryStockRes{Code: 400, Stock: make(map[int32]int32)}

	defer func() {
		if out.Code != 200 {
			glog.Info("QueryStock 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	if len(in.SkuIds) == 0 {
		out.Code = 200
		return
	}

	db := utils.NewDbConn()

	var warehouseId string
	var freezeWarehouseId string

	if in.ChannelId == 5 {
		if DSStock.LastRefreshTime.Add(5 * time.Minute).Before(time.Now()) {
			DSStock.Value = config.GetString("DSStock")
			DSStock.LastRefreshTime = time.Now()
		}
		warehouseId = DSStock.Value
		freezeWarehouseId = warehouseId + ",0" // 电商锁库仓库id = 0
	} else {
		if has, err := db.Table("dc_dispatch.warehouse_relation_shop").
			Where("shop_id = ? and channel_id = ?", in.ShopId, in.ChannelId).
			Select("warehouse_id").Get(&warehouseId); err != nil {
			out.Message = err.Error()
			return
		} else if !has {
			out.Message = "未找到仓库"
			return
		}
		freezeWarehouseId = warehouseId
	}

	var skuGroups []*models.SkuGroup
	// 查一下组合商品
	if err := db.Table("dc_product.sku_group").In("sku_id", in.SkuIds).
		Select("sku_id,group_sku_id,product_type,count").
		Find(&skuGroups); err != nil {
		out.Message = err.Error()
		return
	}
	skuGroupsMap := make(map[int32][]*models.SkuGroup, len(skuGroups))
	for _, group := range skuGroups {
		skuGroupsMap[group.SkuId] = append(skuGroupsMap[group.SkuId], group)
	}

	// 除组合商品id
	var skuIds []string
	for _, id := range in.SkuIds {
		if gs, ok := skuGroupsMap[id]; ok {
			for _, g := range gs {
				if g.ProductType == 1 {
					skuIds = append(skuIds, cast.ToString(g.GroupSkuId))
				}
			}
		} else {
			skuIds = append(skuIds, cast.ToString(id))
		}
	}

	type Stock struct {
		SkuId int32 `json:"sku_id"`
		Qty   int32 `json:"qty"`
	}

	var stocks []*Stock
	stockMap := make(map[int32]*Stock)

	// 因为电商锁库仓库id为0，与实际仓库不对应，所以这里统一用子查询
	if err := db.SQL(fmt.Sprintf(`select wg.goodsid as sku_id, sum(wg.stock) - ifnull(max(f.freeze_stock), 0) as qty
from dc_order.warehouse_goods wg
         left join (select sku_id, sum(stock) as freeze_stock
                    from dc_order.order_freeze_stock
                    where warehouse_id IN (%s) and sku_id in ('%s')
                    group by sku_id) f
                   on f.sku_id = wg.goodsid
where wg.warehouse_id IN (%s) and wg.goodsid in ('%s')
group by wg.goodsid;`, freezeWarehouseId, strings.Join(skuIds, "','"), warehouseId, strings.Join(skuIds, "','"))).Find(&stocks); err != nil {
		out.Message = err.Error()
		return
	}

	for _, stock := range stocks {
		stockMap[stock.SkuId] = stock
	}

	for _, id := range in.SkuIds {
		out.Stock[id] = 0
		// 是组合商品
		if gs, ok := skuGroupsMap[id]; ok {
			for _, child := range gs {
				if child.ProductType == 1 {
					if s, has := stockMap[child.GroupSkuId]; has {
						if s.Qty < 1 || child.Count == 0 {
							out.Stock[id] = 0
							break
						}
						qty := s.Qty / child.Count
						if qty < 1 { // 没有库存直接重置0
							out.Stock[id] = 0
							break
						} else if out.Stock[id] == 0 || qty < out.Stock[id] { // 初始化或者当前商品库存更小，则等于当前库存
							out.Stock[id] = qty
						}
					} else {
						out.Stock[id] = 0
						break
					}
				}
			}
		} else {
			if stock, has := stockMap[id]; has {
				out.Stock[id] = stock.Qty
			}
		}
	}

	out.Code = 200
	return
}

func pullZiLongStock(redisConn *redis.Client, warehouse models.Warehouse, vSkuId int32) models.WarehouseGoods {
	var mysqlEngine = GetDCOrderMysqlConn()
	c := StockService{}
	//门店仓才拉取子龙库存
	zlSkuStock := c.GetZLStockInfo(redisConn, warehouse, []int32{vSkuId})
	mysqlStock := models.WarehouseGoods{
		Goodsid:     strconv.Itoa(int(vSkuId)),
		WarehouseId: warehouse.Id,
	}
	if len(zlSkuStock) > 0 {
		mysqlStock.Stock = int(zlSkuStock[vSkuId])
	}

	var wgs []*models.WarehouseGoods
	if err := mysqlEngine.Table("dc_order.warehouse_goods").Where("warehouse_id = ?", warehouse.Id).
		Select("id,goodsid,stock").Where("goodsid=?", mysqlStock.Goodsid).Find(&wgs); err != nil {
		return mysqlStock
	}

	wgMaps := make(map[string]*models.WarehouseGoods, len(wgs))
	for _, wg := range wgs {
		wgMaps[wg.Goodsid] = wg
	}
	if wg, has := wgMaps[mysqlStock.Goodsid]; has {
		if wg.Stock != mysqlStock.Stock {
			_, err := mysqlEngine.Exec("update dc_order.warehouse_goods set stock = ? where id = ?", mysqlStock.Stock, wg.Id)
			if err != nil {
				glog.Error("更新库存 " + err.Error())
			}
		}
	} else { //如果不存在，直接插入
		mysqlEngine.Insert(&mysqlStock)
	}

	//将获取到的子龙库存落地到数据库 -- 先删除后添加
	//mysqlEngine.Exec("DELETE FROM dc_order.warehouse_goods WHERE warehouse_id = ? AND goodsid = ?;", mysqlStock.WarehouseId, mysqlStock.Goodsid)

	return mysqlStock
}

//处理冻结库存到0的时候通知商品中心，不要显示库存为0的商品
//",stock:skuid,stock:skuid,stock:skuid" 或者 ""
func dealProductStockNoticeParam(warehouseId, skuIdStr string, stock int32) {
	param := pc.StockNoticeRequest{}
	stockStore := &pc.StockStore{
		FinanceCode: fmt.Sprintf("#%s", warehouseId),
	}
	if len(skuIdStr) == 0 {
		return
	} else {
		stockSkuIdStrArray := strings.Split(skuIdStr, ",")
		for _, v := range stockSkuIdStrArray {
			if v == "" {
				continue
			}
			stockSkuIdArray := strings.Split(v, ":")
			if len(stockSkuIdArray) == 1 {
				continue
			}
			skuId, _ := strconv.Atoi(stockSkuIdArray[1])
			if skuId == 0 {
				return
			}
			storeSku := &pc.StoreSku{
				SkuId: int32(skuId),
				Stock: stock,
			}
			stockStore.Skus = append(stockStore.Skus, storeSku)
		}
	}
	param.StockInfo = append(param.StockInfo, stockStore)
	//组合商品查询库存二次通知
	go groupProductStockNotice(param)
}

//通知组合商品的库存信息
func groupProductStockNotice(param pc.StockNoticeRequest) {
	ss, _ := json.Marshal(param)
	glog.Info("冻结或解冻查询库存写入MQ参数：" + string(ss))
	//转换参数,转换成要丢入MQ里面的数据 todo 功能先这样，到时候要考虑传入的如果是财务编码或者是仓库ID的时候怎么处理
	retailSkuStock := dtos.RetailSkuStock{
		App_poi_code: param.StockInfo[0].FinanceCode,
		Food_data:    []dtos.FoodData{},
	}
	var mqInfos []models.MqInfo
	for _, x := range param.StockInfo {

		for _, l := range x.Skus {
			mode := dtos.FoodData{}
			modeSku := dtos.Skus{}
			modeSku.Sku_id = cast.ToString(l.SkuId)
			modeSku.Stock = cast.ToString(l.Stock)
			mode.Skus = append(mode.Skus, modeSku)
			retailSkuStock.Food_data = append(retailSkuStock.Food_data, mode)
		}
	}

	//把消息转成json并丢到mq里面
	mqString, _ := json.Marshal(retailSkuStock)
	glog.Info("冻结或解冻查询库存写入MQ参数：" + string(mqString))

	mqInfo := models.MqInfo{
		Exchange: "datacenter",
		Quene:    "dc_sz_stock_mq_has_stock",
		Content:  string(mqString),
	}
	mqInfos = append(mqInfos, mqInfo)
	dcOrderEngine := GetDCOrderMysqlConn()
	defer dcOrderEngine.Close()
	_, err := dcOrderEngine.Insert(mqInfos)
	if err != nil {
		glog.Info("冻结或解冻查询库存写入MQ报错：", err.Error())
	}

	//conn, ctx, client, cf := GetProductClient()
	//client.GroupProductStockNotice(ctx, &param)
	//conn.Close()
	//cf()
}
