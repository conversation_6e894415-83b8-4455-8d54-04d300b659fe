package utils

import (
	"bytes"
	"crypto/md5"
	"crypto/tls"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/sony/sonyflake"
	"io"
	"io/ioutil"
	"log"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"
)

func HttpPostForm(url, source, ua string, param map[string]interface{}) (int, string) {
	return action(url, source, ua, http.MethodPost, "application/x-www-form-urlencoded", param)
}

func action(uri, source, ua string, httpMethod string, contentType string, param map[string]interface{}) (int, string) {
	defer func() {
		if err := recover(); err != nil {
			log.Println("http.action", err)
		}
	}()
	var req *http.Request
	switch httpMethod {
	case http.MethodGet:
		if param != nil {
			uri += "?" + mapToValues(param).Encode()
		}
		req, _ = http.NewRequest(httpMethod, uri, nil)
	case http.MethodPost:
		httpMethod = http.MethodPost
		var reader io.Reader

		if contentType == "application/x-www-form-urlencoded" {
			reader = strings.NewReader(mapToValues(param).Encode())
		} else if contentType == "application/json;charset=UTF-8" {
			byteData, _ := json.Marshal(param)
			reader = bytes.NewReader(byteData)
		}
		req, _ = http.NewRequest(httpMethod, uri, reader)
		req.Header.Add("Content-Type", contentType)
	default:
		return 0, "不支持的请求类型"
	}

	// for k, v := range httpHeader {
	// 	req.Header.Add(k, v)
	// }
	//ul := uuid.NewV4()
	//sn := strings.ReplaceAll(ul.String(), "-", "")
	//req.Header.Add("sn", sn)
	//req.Header.Add("source", source)
	//req.Header.Add("ua", ua)
	//req.Header.Add("timestamp", strconv.Itoa(int(time.Now().Unix())))

	client := http.Client{Timeout: time.Second * 30, Transport: &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}}}

	res, err := client.Do(req)
	if err != nil {
		glog.Error(err)
		return 0, err.Error()
	}

	defer res.Body.Close()
	body, _ := ioutil.ReadAll(res.Body)
	return res.StatusCode, string(body)
}

func mapToValues(mp map[string]interface{}) url.Values {
	v := url.Values{}
	for key, val := range mp {
		switch val.(type) {
		case int:
			v.Add(key, strconv.Itoa(val.(int)))
		case int32:
			v.Add(key, strconv.Itoa(int(val.(int32))))
		case int64:
			v.Add(key, strconv.Itoa(int(val.(int64))))
		case float64:
			v.Add(key, strconv.FormatFloat(val.(float64), 'E', -1, 64))
		case float32:
			v.Add(key, strconv.FormatFloat(float64(val.(float32)), 'E', -1, 32))
		default:
			v.Add(key, val.(string))
		}
	}
	//glog.Info(v.Encode())
	return v
}

//北京acp接口公用参数
func CreateCommonBjAcpParam() map[string]interface{} {
	dataArr := make(map[string]interface{})

	apiid := config.GetString("BJAuth.AppId")
	apiSecret := config.GetString("BJAuth.ApiSecret")
	apiStr := GenSonyflake() //自己生成，唯一的十六位随机字符串
	timestamp := strconv.Itoa(int(time.Now().Unix()))
	sign := fmt.Sprintf("apiSecret=%s&apiStr=%s&apiId=%s&timestamp=%s&apiSecret=%s", apiSecret, apiStr, apiid, timestamp, apiSecret)
	h := md5.New()
	h.Write([]byte(sign))
	md5sign := strings.ToUpper(hex.EncodeToString(h.Sum(nil)))

	dataArr["apiId"] = apiid
	dataArr["apiStr"] = apiStr
	dataArr["timestamp"] = timestamp
	dataArr["sign"] = md5sign

	return dataArr
}

//生成16位唯一字符编码
func GenSonyflake() string {
	flake := sonyflake.NewSonyflake(sonyflake.Settings{})
	id, err := flake.NextID()

	if err == nil {
		return fmt.Sprintf("b%x", id)
	}
	return ""
}

//url ： /base/area/all
//dataJson : 数据对象转化成json字符串
func HttpPostToBJ(url string, dataJson []byte, Headers string) ([]byte, error) {
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(dataJson))
	client := http.Client{Timeout: time.Second * 60, Transport: &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}}}
	req.Header.Set("Content-Type", "application/json")

	if len(Headers) > 0 {
		strlist := strings.Split(Headers, "&")
		for i := 0; i < len(strlist); i++ {
			v := strlist[i]
			valuelist := strings.Split(v, "|")
			req.Header.Set(valuelist[0], valuelist[1])
		}
	}

	for k, v := range BjSignMap(url) {
		req.Header.Set(k, v)
	}

	res, err := client.Do(req)
	if err != nil {
		log.Println(err)
		return []byte(""), err
	}
	defer res.Body.Close()
	body, err := ioutil.ReadAll(res.Body)
	return body, err
}

func BjSignMap(url string) map[string]string {
	domainUrl := strings.Split(url, "//")[1]
	baseUrl := strings.Split(domainUrl, "/")[0]
	method := strings.Split(url, baseUrl)[1]
	Timestamp := strconv.Itoa(int(time.Now().Unix()))
	sign := fmt.Sprintf("AppId=%s&Secret=%s&Url=%s&Timestamp=%s&Version=%s", config.GetString("BJAuth.AppId"), config.GetString("BJAuth.Secret"), method, Timestamp, config.GetString("BJAuth.Version"))
	h := md5.New()
	h.Write([]byte(sign))
	md5sign := strings.ToUpper(hex.EncodeToString(h.Sum(nil)))
	arr := make(map[string]string)
	arr["focus-auth-appid"] = config.GetString("BJAuth.AppId")
	arr["focus-auth-userid"] = "0"
	arr["focus-auth-username"] = "0"
	arr["focus-auth-version"] = config.GetString("BJAuth.Version")
	arr["focus-auth-url"] = method
	arr["focus-auth-timestamp"] = Timestamp
	arr["focus-auth-sign"] = md5sign

	/*	ff,_:=json.Marshal(arr)

		fmt.Println(string(ff))*/
	return arr
}
func MapToJson(param map[string]interface{}) string {
	dataType, _ := json.Marshal(param)
	dataString := string(dataType)
	return dataString
}
