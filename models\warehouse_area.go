package models

import (
	"time"
)

type WarehouseArea struct {
	Id          int       `xorm:"not null pk autoincr comment('自增') INT(11)"`
	Areaid      int       `xorm:"not null default 0 comment('区域id') INT(11)"`
	Warehouseid int       `xorm:"not null default 0 comment('仓库id') INT(11)"`
	Level       int       `xorm:"not null default 1 comment('区域等级(1-一级，2-二级，3-3级)') INT(11)"`
	Lastdate    time.Time `xorm:"not null default 'current_timestamp()' comment('最后更新时间') TIMESTAMP"`
}
