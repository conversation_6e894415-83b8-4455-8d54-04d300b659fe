package services

import (
	"_/proto/ic"
	"_/proto/pc"
	"_/utils"
	"context"
	"reflect"
	"testing"
	"time"

	"google.golang.org/grpc"
)

var ss = make(map[int]int)

func TestGetBaseClient(t *testing.T) {
	type args struct {
		grpcserverkey string
		defalutvalue  string
	}
	tests := []struct {
		name  string
		args  args
		want  *grpc.ClientConn
		want1 context.Context
		want2 pc.DcProductClient
		want3 context.CancelFunc
	}{
		// TODO: Add test cases.
		{name: "测试redis"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			//初始化redis
			redisConn := utils.ConnectClusterRedis()
			defer redisConn.Close()

			lockRes := redisConn.SetNX(stockTask, time.Now().Unix(), time.Minute*1).Val()
			if !lockRes {
				return
			}

			got, got1, got2, got3 := GetBaseClient(tt.args.grpcserverkey, tt.args.defalutvalue)
			if !reflect.DeepEqual(got, tt.want) {
				t.<PERSON><PERSON><PERSON>("GetBaseClient() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("GetBaseClient() got1 = %v, want %v", got1, tt.want1)
			}
			if !reflect.DeepEqual(got2, tt.want2) {
				t.Errorf("GetBaseClient() got2 = %v, want %v", got2, tt.want2)
			}
			if !reflect.DeepEqual(got3, tt.want3) {
				t.Errorf("GetBaseClient() got3 = %v, want %v", got3, tt.want3)
			}
		})
	}
}

func TestGetProductClient(t *testing.T) {
	tests := []struct {
		name  string
		want  *grpc.ClientConn
		want1 context.Context
		want2 pc.DcProductClient
		want3 context.CancelFunc
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1, got2, got3 := GetProductClient()
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetProductClient() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("GetProductClient() got1 = %v, want %v", got1, tt.want1)
			}
			if !reflect.DeepEqual(got2, tt.want2) {
				t.Errorf("GetProductClient() got2 = %v, want %v", got2, tt.want2)
			}
			if !reflect.DeepEqual(got3, tt.want3) {
				t.Errorf("GetProductClient() got3 = %v, want %v", got3, tt.want3)
			}
		})
	}
}

//ProvideSyncStock
func BenchmarkZilongService_ProvideSyncStock(b *testing.B) {
	ToProvideSyncStock()
}

func TestToProvideSyncStock(t *testing.T) {
	tests := []struct {
		name string
	}{
		// TODO: Add test cases.
		//

		{name: "ToProvideSyncStock"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
		})
	}
}

func TestGetBaseClient1(t *testing.T) {
	type args struct {
		grpcserverkey string
		defalutvalue  string
	}
	tests := []struct {
		name  string
		args  args
		want  *grpc.ClientConn
		want1 context.Context
		want2 pc.DcProductClient
		want3 context.CancelFunc
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1, got2, got3 := GetBaseClient(tt.args.grpcserverkey, tt.args.defalutvalue)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetBaseClient() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("GetBaseClient() got1 = %v, want %v", got1, tt.want1)
			}
			if !reflect.DeepEqual(got2, tt.want2) {
				t.Errorf("GetBaseClient() got2 = %v, want %v", got2, tt.want2)
			}
			if !reflect.DeepEqual(got3, tt.want3) {
				t.Errorf("GetBaseClient() got3 = %v, want %v", got3, tt.want3)
			}
		})
	}
}

func TestGetProductClient1(t *testing.T) {
	tests := []struct {
		name  string
		want  *grpc.ClientConn
		want1 context.Context
		want2 pc.DcProductClient
		want3 context.CancelFunc
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1, got2, got3 := GetProductClient()
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetProductClient() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("GetProductClient() got1 = %v, want %v", got1, tt.want1)
			}
			if !reflect.DeepEqual(got2, tt.want2) {
				t.Errorf("GetProductClient() got2 = %v, want %v", got2, tt.want2)
			}
			if !reflect.DeepEqual(got3, tt.want3) {
				t.Errorf("GetProductClient() got3 = %v, want %v", got3, tt.want3)
			}
		})
	}
}

func TestToProvideSyncStock1(t *testing.T) {
	tests := []struct {
		name string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
		})
	}
}

func TestZilongService_AddStockTask(t1 *testing.T) {
	type args struct {
		ctx context.Context
		in  *ic.AddStockTaskRequest
	}

	var params ic.AddStockTaskRequest
	params.Request = `[{"zilong_id":"7521","GoodsList":[{"GoodsID":"C0819XX132","Stock":65}]},{"zilong_id":"7474","GoodsList":[{"GoodsID":"L000003445","Stock":7},{"GoodsID":"L000002636","Stock":100}]},{"zilong_id":"6165","GoodsList":[{"GoodsID":"C1853XX335","Stock":54},{"GoodsID":"C1649XX304","Stock":176},{"GoodsID":"C1239XX244","Stock":35},{"GoodsID":"C010110005","Stock":61},{"GoodsID":"C010102032","Stock":88},{"GoodsID":"C000R9JKYM","Stock":158}]}]`
	params.SerialNumber = "1e97c09939e549b3833016624a3c46fd"
	params.StockType = 1
	tests := []struct {
		name    string
		args    args
		want    *ic.AddStockTaskResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "getstockinfo",
			args: args{
				ctx: nil,
				in:  &params,
			}},
	}
	for _, tt := range tests {
		t1.Run(tt.name, func(t1 *testing.T) {
			t := ZilongService{}
			got, err := t.AddStockTask(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t1.Errorf("AddStockTask() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t1.Errorf("AddStockTask() got = %v, want %v", got, tt.want)
			}
		})
	}
}
func TestZilongService_(t1 *testing.T) {
	type args struct {
		ctx context.Context
		in  *ic.QueryStockTaskRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *ic.QueryStockTaskResponse
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t1.Run(tt.name, func(t1 *testing.T) {
			t := ZilongService{}
			got, err := t.QueryStockTask(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t1.Errorf("QueryStockTask() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t1.Errorf("QueryStockTask() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestToProvideSyncStock2(t *testing.T) {
	tests := []struct {
		name string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
		})
	}
}
