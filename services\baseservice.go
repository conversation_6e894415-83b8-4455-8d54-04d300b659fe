package services

import (
	"_/dtos"
	"_/models"
	"context"
	"encoding/json"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	rp_kit "github.com/tricobbler/rp-kit"
	"google.golang.org/grpc/metadata"
)

//获取全聚到配置参数 对应美团的
func initQQD() dtos.QqdConfig {
	var qqdCon = dtos.QqdConfig{}
	qqdCon.AppkeyCon = config.GetString("qqd.new.appkey")
	qqdCon.AppsecretCon = config.GetString("qqd.new.appsecret")
	qqdCon.Token = config.GetString("qqd.new.token")
	qqdCon.ApiUrl = config.GetString("qqd.apiUrl")
	return qqdCon
}

func loadLoginUserInfo(ctx context.Context) *models.LoginUserInfo {

	var (
		grpcContext models.GrpcContext
		userInfo    models.LoginUserInfo
	)
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		if md.Get("grpc_context") == nil {
			glog.Error("grpc_context==nil")
		}

		if err := json.Unmarshal([]byte(md.Get("grpc_context")[0]), &grpcContext); err != nil {
			glog.Error(err)
		}

	} else {
		glog.Error("grpc context 加载用户登录信息失败")
	}
	userInfo = grpcContext.UserInfo
	userInfo.RealName = rp_kit.UrlDecode(userInfo.RealName)
	if userInfo.UserNo == "" {
		return nil
	} else {
		return &userInfo
	}
}
