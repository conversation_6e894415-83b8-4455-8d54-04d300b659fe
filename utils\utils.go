package utils

import (
	"bytes"
	"crypto/hmac"
	"crypto/md5"
	"crypto/sha1"
	"crypto/tls"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/limitedlee/microservice/common/config"
	kit "github.com/tricobbler/rp-kit"
	"io"
	"io/ioutil"
	"log"
	"math"
	"mime/multipart"
	"net/http"
	"net/url"
	"os"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/maybgit/glog"
)

const (
	ACCESS_FROM_USER = 0
	COLON            = ":"
)

//七牛上传结果
type QiNiuUploadResult struct {
	Url string `json:"url"`
	Err string `json:"error"`
}

// 将处理失败的商品信息导入excel上传至七牛云
func UploadExcelToQiNiu(fileName string) (url string, err error) {
	defer kit.CatchPanic()

	bodyBuffer := &bytes.Buffer{}
	bodyWriter := multipart.NewWriter(bodyBuffer)

	fileWriter, _ := bodyWriter.CreateFormFile("file", fileName)

	fd, err := os.Open(fileName)
	if err != nil {
		return "", errors.New("打开文件失败，" + err.Error())
	}
	defer fd.Close()

	io.Copy(fileWriter, fd)
	contentType := bodyWriter.FormDataContentType()
	bodyWriter.Close()

	glog.Info("上传开始")
	// 上传文件
	path := config.GetString("file-upload-url") + "/fss/up"
	resp, err := http.Post(path, contentType, bodyBuffer)
	if err != nil {
		return "", errors.New("上传接口接口调用失败，" + err.Error())
	}
	defer resp.Body.Close()

	glog.Info("上传完成")
	respBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", errors.New("读取body失败，" + err.Error())
	}

	if len(respBody) == 0 {
		return "", errors.New("读取body失败，body为空")
	}

	result := new(QiNiuUploadResult)
	if err = json.Unmarshal(respBody, result); err != nil {
		return "", errors.New("解析json失败，" + err.Error() + "，json：" + string(respBody))
	}
	glog.Info("解析下载连接完成，", result.Url)

	if len(result.Url) == 0 {
		return "", errors.New("七牛云上传失败，" + result.Err)
	}

	return result.Url, nil
}

//PrintJSON 将struct序列化json打印日志
func PrintJSON(inter interface{}) {
	bt, _ := json.Marshal(inter)
	log.Println("json", string(bt))
}

//生成32位md5字串
func GetMd5String(s string) string {
	h := md5.New()
	h.Write([]byte(s))
	return hex.EncodeToString(h.Sum(nil))
}

//生成32位Guid字串
func GetGuid() string {
	return strings.ReplaceAll(uuid.New().String(), "-", "")
}

//生成36位Guid字串
func GetGuid36() string {
	return uuid.New().String()
}

//GenCode 生成编号，前缀+随机数（或者指定的某个数字）+数量（数量位数不足5，会前置补0）
func GenCode(prefix string, rand int, count int) string {
	var strs []string
	strs = append(strs, prefix)
	strs = append(strs, time.Now().Local().Format("20060102150405"))
	strs = append(strs, strconv.Itoa(rand))
	strCount := strconv.Itoa(int(count))
	for index := 0; index < 5-len(strCount); index++ {
		strs = append(strs, "0")
	}
	strs = append(strs, strCount)
	return strings.Join(strs, "")
}

//全角转换半角
func DBCtoSBC(s string) string {
	retstr := ""
	for _, i := range s {
		inside_code := i
		if inside_code == 12288 {
			inside_code = 32
		} else {
			inside_code -= 65248
		}
		if inside_code < 32 || inside_code > 126 {
			retstr += string(i)
		} else {
			retstr += string(inside_code)
		}
	}
	return retstr
}

//根据阿里加密规则，获取用户名
func GetUserName(ak string, resourceOwnerId uint64) string {
	var buffer bytes.Buffer
	buffer.WriteString(strconv.Itoa(ACCESS_FROM_USER))
	buffer.WriteString(COLON)
	buffer.WriteString(strconv.FormatUint(resourceOwnerId, 10))
	buffer.WriteString(COLON)
	buffer.WriteString(ak)
	return base64.StdEncoding.EncodeToString(buffer.Bytes())
}

//根据阿里加密规则，获取密码
func GetPassword(sk string) string {
	now := time.Now()
	currentMillis := strconv.FormatInt(now.UnixNano()/1000000, 10)
	var buffer bytes.Buffer
	buffer.WriteString(strings.ToUpper(HmacSha1(currentMillis, sk)))
	buffer.WriteString(COLON)
	buffer.WriteString(currentMillis)
	glog.Info(currentMillis)
	glog.Info(HmacSha1(sk, currentMillis))
	return base64.StdEncoding.EncodeToString(buffer.Bytes())
}

//加密方式
func HmacSha1(keyStr string, message string) string {
	key := []byte(keyStr)
	mac := hmac.New(sha1.New, key)
	mac.Write([]byte(message))
	return hex.EncodeToString(mac.Sum(nil))
}

//将float64转成精确的int64
func Round(f float64) int {
	return int(math.Floor(f + 0.5))
}

// 增加重试机制
func HttpPost(url string, bytesData []byte, contentType string, retryCount int32) ([]byte, error) {
	if retryCount > 3 {
		return nil, errors.New("重试超过3次")
	} else {
		time.Sleep(time.Second * time.Duration(30*retryCount))
	}
	//跳过证书验证
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	reader := bytes.NewReader(bytesData)
	request, err := http.NewRequest("POST", url, reader)
	if err != nil {
		fmt.Println(err.Error())
		return nil, err
	}
	if len(contentType) > 0 {
		request.Header.Set("Content-Type", contentType) //"application/json;charset=UTF-8"
	}
	client := &http.Client{Transport: tr}
	resp, err := client.Do(request)
	if err != nil {
		return nil, err
	}
	if resp.StatusCode != 200 {
		return nil, errors.New(resp.Status)
	}
	respBytes, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	return respBytes, nil
}

//获取签名(正向接口)
//par: 应用参数的JSON ，methodName 调用API的名称,返回拼接好的
func Sign(par string, methodName string, tokenStr string, app_key string, appSecret string) string {

	format := "json"
	method := methodName
	sign_method := "md5"
	timestamp := time.Now().Format("2006-01-02 15:04:05")
	token := tokenStr
	v := "1.0"
	body := par

	var slice1 []string
	//slice1 := make([]string, ParLen)
	slice1 = append(slice1, "app_key"+app_key)
	slice1 = append(slice1, "format"+format)
	slice1 = append(slice1, "method"+method)
	slice1 = append(slice1, "sign_method"+sign_method)
	slice1 = append(slice1, "timestamp"+timestamp)
	slice1 = append(slice1, "v"+v)

	//如果是获取token的API就不需要TOKEN参数
	if methodName != "emall.token.get" {
		slice1 = append(slice1, "token"+token)
	}
	sort.Strings(slice1)
	content := appSecret
	for i := 0; i < len(slice1); i++ {
		content += slice1[i]
	}
	content += body
	content += appSecret
	sgin := strings.ToUpper(GetMd5String(content))
	retrunSgin := "method=" + method + "&timestamp=" + url.QueryEscape(timestamp) + "&format=" + format + "&app_key=" + app_key + "&v=" + v + "&sign_method=" + sign_method
	if methodName != "emall.token.get" {
		retrunSgin += "&token=" + token
	}
	retrunSgin += "&sign=" + sgin
	return retrunSgin
}
