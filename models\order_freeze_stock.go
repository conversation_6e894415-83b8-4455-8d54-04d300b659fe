package models

import (
	"errors"
	"github.com/go-xorm/xorm"
	"time"
)

type OrderFreezeStock struct {
	Id          int       `xorm:"not null pk autoincr comment('自增主键') INT(11)"`
	OrderSn     string    `xorm:"not null default '''' comment('订单编号') index VARCHAR(50)"`
	SkuId       string    `xorm:"not null default '''' comment('商品skuid') index VARCHAR(50)"`
	Stock       int       `xorm:"not null default 1 comment('冻结库存数量') INT(11)"`
	WarehouseId int       `xorm:"not null default 0 comment('仓库id') INT(11)"`
	CreateTime  time.Time `xorm:"default 'current_timestamp()' comment('创建时间') DATETIME"`
	UpdateTime  time.Time `xorm:"default 'current_timestamp()' comment('更新时间') DATETIME"`
}

// QueryFreezeStocks 查询冻结库存信息
func QueryFreezeStocks(db *xorm.Engine, warehouseId int, skuIds []string) (fss map[string]int, err error) {
	// 查询所有的冻结库存信息
	var freezes []*OrderFreezeStock
	if err = db.Table("dc_order.order_freeze_stock").Select("sku_id,sum(stock) as stock").
		Where("warehouse_id = ?", warehouseId).In("sku_id", skuIds).
		GroupBy("sku_id").Find(&freezes); err != nil {
		return nil, errors.New("查询冻结库存" + err.Error())
	}

	fss = make(map[string]int, len(freezes))
	for _, freeze := range freezes {
		fss[freeze.SkuId] = freeze.Stock
	}
	return
}
