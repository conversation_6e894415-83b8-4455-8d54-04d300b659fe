package services

import (
	"_/proto/pc"
	"context"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"google.golang.org/grpc"
	"time"
)

/*
Grpc服务端集合
*/

//product-center服务客户端
func GetProductClient() (*grpc.ClientConn, context.Context, pc.DcProductClient, context.CancelFunc) {
	return GetBaseClient("grpc.product-center", "127.0.0.1:11003")
}

func GetBaseClient(grpcServerKey, defaultValue string) (*grpc.ClientConn, context.Context, pc.DcProductClient, context.CancelFunc) {
	productAddress := config.GetString(grpcServerKey)
	if productAddress == "" {
		productAddress = defaultValue
	}
	if conn, err := grpc.Dial(productAddress, grpc.WithInsecure()); err != nil {
		glog.Error(err)
		return nil, nil, nil, nil
	} else {
		client := pc.NewDcProductClient(conn)
		ctx, cf := context.WithTimeout(context.Background(), time.Second*90)
		return conn, ctx, client, cf
	}
}
