package services

import (
	"_/dtos"
	"_/models"
	"_/proto/ic"
	"_/proto/pc"
	"_/utils"
	"context"
	"encoding/json"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/go-redis/redis"
	"github.com/limitedlee/microservice/common/config"
	logger "github.com/maybgit/glog"
	"github.com/spf13/cast"
)

const (
	//分布式锁
	stockTask = "task:lock:zl_stock"
)

type ZilongService struct {
}

//提供给子龙系统实时推送库存接口
//第一步：获取redis所有的子龙和财务对应关系表
//第二步：循环子龙财务关系表，根据财务code获取库存中心的仓库id
//第三步：循环传进来的参数，第一次循环子龙id,根据子龙id获取仓库id,并去库存中心查看该仓库id的类型以及是否可用，可用则继续循环传进来的商品集合，并开始数据处理，否则，继续一条
func (t ZilongService) ProvideSyncStock(ctx context.Context, in *ic.ProvideSyncStockRequest, warehouseStoreRelation dtos.WarehouseStoreRelation) (*ic.ProvideSyncStockResponse, error) {
	var out = new(ic.ProvideSyncStockResponse)
	//判断非空
	if len(in.GoodsStock) == 0 {
		out.Code = 400
		out.Message = "参数不能为空"
		return out, nil
	}

	//初始化数据库
	connDcDispatch := GetMysqlConn()
	defer connDcDispatch.Close()

	for _, v := range in.GoodsStock { //5个仓
		warehouse := models.Warehouse{
			Code:     warehouseStoreRelation.Code,
			Id:       warehouseStoreRelation.WarehouseId,
			Category: warehouseStoreRelation.Category,
			Status:   warehouseStoreRelation.Status,
		}
		//conn_dc_dispatch.Where("id =?", warehouseStoreRelationMap[v.ZilongId].WarehouseId).And("category=3").And("status=1").Get(&warehouse)
		if warehouse.Id > 0 { //判断是否是有效的门店仓
			var mapGoodsQty []dtos.GoodsStock
			goodsQtyMap := make(map[string]int32, 0)
			var goodsId []string
			//获取所有需要处理的数据
			var allGoodsId []string
			ind := 1000
			for _, m := range v.GoodsList {
				allGoodsId = append(allGoodsId, m.GoodsId)
				goodsQtyMap[m.GoodsId] = m.Stock
			}
			for {
				if len(allGoodsId) < ind {
					ind = len(allGoodsId)
				}
				_list := allGoodsId[:ind]
				channelSkuThirdList := []models.SkuThird{}
				connDcDispatch.Table("dc_product.channel_sku_third").
					Select("product_id,sku_id,third_sku_id").
					Where("erp_id = 4").
					In("third_sku_id", _list).
					GroupBy("product_id,sku_id").
					Find(&channelSkuThirdList)
				for _, v := range channelSkuThirdList {
					goodsId = append(goodsId, strconv.Itoa(int(v.SkuId)))
					var _mapQty = dtos.GoodsStock{
						SpuId:   strconv.Itoa(int(v.ProductId)),
						GoodsId: strconv.Itoa(int(v.SkuId)),
						Stock:   int(goodsQtyMap[v.ThirdSkuId]),
					}
					mapGoodsQty = append(mapGoodsQty, _mapQty)
				}
				allGoodsId = allGoodsId[ind:]
				if len(allGoodsId) == 0 {
					break
				}
			}

			if len(mapGoodsQty) > 0 {
				logger.Info("本次同步数据数量有：", len(mapGoodsQty), " 仓库id:", warehouse.Id)
				//写入数据库和redis和mq
				writeStock(warehouse, goodsId, mapGoodsQty)
				logger.Info("本次同步数据数量执行完成", " 仓库id:", warehouse.Id)
			}
		}

	}
	out.Code = 200
	out.Message = "Success"
	return out, nil
}

//定时任务：每5分钟推送一次
func (t ZilongService) AutoToMQ() {
	//todo 查询warehouse 的status=1的category=3|4的所有仓库，循环仓库id,去 redis里面找库存，
	// 用redis的实时库存-已冻结的库存，找到结果后，把数据丢到mq，由李珊那边订阅消费---每5分钟执行一次
	//redis 的库存存储数据结构为stock:商品id:仓库id
	//获取配置中心的mq配置
	dcSzStockMq := config.GetString("StockMQ")

	conn := GetMysqlConn()
	var list []models.Warehouse
	conn.Where("status=1").And("category=3 or category=4").Find(&list)
	redisconn := utils.ConnectClusterRedis() //开启redis管道
	for _, v := range list {
		var rstock dtos.RetailSkuStock
		//如果是门店仓
		if v.Category == 3 { //thirdid（财务code）去redis找到对应的美团门店id
			mt_id := redisconn.HGet("store:relation:dctomt", v.Thirdid).Val()
			rstock.App_poi_code = mt_id
		} else if v.Category == 4 { //如果是前置仓,前置仓通过warehouse_relationship的shop_id（财务code,去redis获取得到对应的关系）
			var wr_model models.WarehouseRelationship
			conn.Where("warehouse_id=?", v.Id).Get(&wr_model)
			if wr_model.Id > 0 && len(wr_model.ShopId) > 0 {
				mt_id := redisconn.HGet("store:relation:dctomt", wr_model.ShopId).Val()
				rstock.App_poi_code = mt_id
			}
		}
		//todo 16384表示redis的卡槽位，如果使用的是集群的话，这里的数量为16384*集群的数量
		// 获取该仓库的所有商品信息
		allgoods, _ := redisconn.Scan(0, "stock:*:"+strconv.Itoa(v.Id), 16384).Val()
		pipe := redisconn.Pipeline()
		defer pipe.Close()
		for _, t := range allgoods {
			pipe.Get(t)
		}
		result, _ := pipe.Exec()
		for _, cmder := range result {
			cmd := cmder.(*redis.StringCmd)
			key := cmd.Args()[1]      //key
			val := cmd.Val()          //值
			keyString := key.(string) //interface转换string
			str2 := strings.Split(keyString, ":")
			//获取该商品已被冻结的数量
			//freezestock := GetGoodsNumber("Freeze:*:"+str2[1]+"*", redisconn)

			freezestock := 0
			result, _ := pipe.HScan("Freeze:"+str2[1], 0, "*", pipe.HLen("Freeze:"+str2[1]).Val()*2).Val()
			for i, s := range result {
				if i%2 == 1 {
					i, _ := strconv.Atoi(s)
					freezestock = freezestock + i
				}
			}

			valInt, _ := strconv.Atoi(val)
			newStock := valInt - freezestock //可用的最新的库存
			var Skus dtos.Skus
			Skus.Stock = strconv.Itoa(int(newStock))
			//sku todo 根据货号获取sku
			var params pc.SkuThirdListRequest
			params.Systemid = "4"
			params.Thirdskuid = keyString
			conn, ctx, client, cf := GetProductClient()
			defer conn.Close()
			defer cf()
			ic_out, err := client.QuerySkuThirdList(ctx, &params)
			if err != nil {
				logger.Error("根据货号获取sku失败：", err)
			}
			sku := ic_out.Details[0].SkuId
			Skus.Sku_id = strconv.Itoa(int(sku))
			var FoodData dtos.FoodData
			App_food_code := ic_out.Details[0].ProductId
			FoodData.App_food_code = strconv.Itoa(int(App_food_code))
			FoodData.Skus = append(FoodData.Skus, Skus)
			rstock.Food_data = append(rstock.Food_data, FoodData)

			//把消息转成json并丢到mq里面
			mqstring, _ := json.Marshal(rstock)
			code, err := PulishMq(dcSzStockMq, "datacenter", string(mqstring))
			if err != nil && code != 200 {
				//如果消息没有推送成功，这里写入日志
				logger.Error("定时推送美团消息队列失败，推送消息内容为：", string(mqstring))
			}
		}
	}
}

//根据流水查询数据库是否存在未完成的任务
func (t ZilongService) QueryStockTask(ctx context.Context, in *ic.QueryStockTaskRequest) (*ic.QueryStockTaskResponse, error) {
	out := new(ic.QueryStockTaskResponse)
	if len(in.SerialNumber) <= 0 {
		return out, nil
	}
	conn := GetMysqlConn()
	defer conn.Close()
	var model models.StockTask
	conn.Where("serial_number=?", in.SerialNumber).And("status=1").Get(&model)
	IsEmpty := reflect.DeepEqual(model, models.StockTask{})
	if IsEmpty {
		return out, nil
	}
	out.Id = model.Id
	out.Request = model.Request
	out.SerialNumber = model.SerialNumber
	out.Status = model.Status
	out.CreateDate = model.CreateDate.Format("2006-01-02 15:04:05")
	return out, nil
}

//新增任务记录
//todo 2021年3月6日
//todo 张震
//todo 修改落地到数据库查询不方便的逻辑，单个仓一条数据，便于查询是否推送
func (t ZilongService) AddStockTask(ctx context.Context, in *ic.AddStockTaskRequest) (*ic.AddStockTaskResponse, error) {
	out := new(ic.AddStockTaskResponse)
	if len(in.Request) <= 0 || len(in.SerialNumber) <= 0 {
		out.Code = 400
		out.Message = "请求参数不能为空"
		logger.Info("AddStockTask ：请求参数不能为空")
		return out, nil
	}
	var params []dtos.StockGoods
	err := json.Unmarshal([]byte(in.Request), &params)
	if err != nil {
		out.Code = 400
		out.Message = "反序列化失败，err:" + err.Error()
		logger.Error("AddStockTask 反序列化失败，err:", err.Error())
		return out, nil
	}
	var list []models.StockTask
	for _, v := range params {
		var model models.StockTask
		model.Status = 0
		string_request, err := json.Marshal(v)
		if err != nil {
			logger.Error("AddStockTask 序列化失败，错误:", err.Error())
			continue
		}
		model.Request = string(string_request)
		model.SerialNumber = in.SerialNumber
		model.CreateDate = time.Now()
		model.LastDate = time.Now()
		if in.StockType == 0 {
			model.StockType = 1
		} else {
			model.StockType = in.StockType
		}
		list = append(list, model)
	}
	conn := GetMysqlConn()
	defer conn.Close()
	_, err = conn.Insert(&list)
	if err != nil {
		out.Code = 400
		out.Message = "StockTask失败:" + err.Error()
		logger.Info("StockTask失败 ：" + err.Error())
		return out, nil
	}
	out.Code = 200
	return out, nil
}

//定时任务,5分钟更新库存
func ToProvideSyncStock() {
	//初始化redis
	redisConn := utils.ConnectClusterRedis()
	defer redisConn.Close()

	delRedisSetNx := DelRedisSetNx(redisConn, stockTask, 60) //5分钟的判断
	if delRedisSetNx {
		lockRes := redisConn.SetNX(stockTask, time.Now().Unix(), time.Minute*60).Val()
		if !lockRes {
			return
		}
	} else {
		logger.Info("库存锁没释放")
		return
	}
	defer func() {
		for {
			_, err := redisConn.Del(stockTask).Result()
			if err != nil {
				logger.Error("SyncStock删除锁出错:" + err.Error())
				time.Sleep(time.Second * 5)
			} else {
				break
			}
		}
	}()

	conn := GetMysqlConn()
	defer conn.Close()
	var stockTaskList []models.StockTask
	nowStr := time.Now().Add(-time.Hour * 24).Format("2006-01-02 15:04:05")
	err := conn.Where("status=0 and stock_type=1 and do_count<2 and create_date>?", nowStr).Asc("create_date").Limit(50).Find(&stockTaskList)
	if err != nil {
		logger.Info("子龙查询要处理的库存出错！ " + err.Error())
	}
	isAll := GetIsAll()
	stockIds := make([]string, 0)
	stockDataMap := make(map[string]models.StockTask)
	for _, v := range stockTaskList {
		//如果不跑全量库存，直接修改数据状态
		if !isAll {
			if strings.Contains(v.SerialNumber, "-ALL-") || strings.Contains(v.SerialNumber, "-HOSPITAL-") {
				model := models.StockTask{
					Status:   1,
					LastDate: time.Now(),
				}
				_, err := conn.Id(v.Id).Update(&model)
				if err != nil {
					logger.Error("更新stock_task状态错误" + err.Error())
				}
				continue
			}
		}
		stockIds = append(stockIds, cast.ToString(v.Id))
		stockDataMap[cast.ToString(v.Id)] = v
	}

	if len(stockIds) > 0 {
		//转换写入库存的接口参数
		var request ic.ProvideSyncStockRequest
		warehouseStoreRelationMap := make(map[string]dtos.WarehouseStoreRelation)
		for _, v := range stockDataMap {
			var goodsStockList ic.GoodsStockList
			var stockGoods dtos.StockGoods
			err := json.Unmarshal([]byte(v.Request), &stockGoods)
			if err != nil {
				logger.Error("ToProvideSyncStock解析报错"+"ID:"+cast.ToString(v.Id), err)
				continue
			}
			goodsStockList.ZilongId = stockGoods.ZilongID

			//获取子龙仓库信息
			financeCode := redisConn.HGet("store:relation:zltodc", stockGoods.ZilongID).Val()
			if financeCode == "" {
				logger.Error("无法获取到对应的仓库信息:", stockGoods.ZilongID)
				continue
			}
			//根据财务编码找出对应的门店仓库

			// 找出门店仓仓库
			var warehouse models.Warehouse
			_, err = conn.Where("code=? and category=3 ", financeCode).Get(&warehouse)
			if err != nil {
				logger.Error("无法获取到对应的仓库信息:", financeCode, err.Error())
				continue
			}
			if warehouse.Code == "" {
				logger.Errorf("WarehouseStoreRelation没有找到对应的仓库:%s %s", financeCode)
				continue
			}

			var warehouseStoreRelation dtos.WarehouseStoreRelation
			warehouseStoreRelation.ZilongId = stockGoods.ZilongID
			warehouseStoreRelation.FanceCode = financeCode
			warehouseStoreRelation.WarehouseId = warehouse.Id
			warehouseStoreRelation.Code = warehouse.Code
			warehouseStoreRelation.Category = warehouse.Category
			warehouseStoreRelation.Status = warehouse.Status
			warehouseStoreRelationMap[stockGoods.ZilongID] = warehouseStoreRelation

			for _, t := range stockGoods.GoodsList {
				var modelDetails ic.GoodsStock
				modelDetails.Stock = int32(t.Stock)
				modelDetails.GoodsId = t.GoodsID
				goodsStockList.GoodsList = append(goodsStockList.GoodsList, &modelDetails)
			}
			request.GoodsStock = append(request.GoodsStock, &goodsStockList)
		}

		oldCount := len(request.GoodsStock)
		//用限流方式去解决超大数据量
		successCount := 0
		l := 5 //这里的5，指的是5家门店的数量
		for {
			if len(request.GoodsStock) < l {
				l = len(request.GoodsStock)
			}
			_list := request.GoodsStock[:l]

			var wg sync.WaitGroup //定义一个同步等待的组

			for _, l := range _list {
				wg.Add(1)
				go func(GoodsStock *ic.GoodsStockList, warehouseStoreRelation dtos.WarehouseStoreRelation) {
					var modelRequest ic.ProvideSyncStockRequest
					//去做同步操作
					var t ZilongService
					modelRequest.GoodsStock = append(modelRequest.GoodsStock, GoodsStock)
					//库存落地
					icOut, err := t.ProvideSyncStock(context.Background(), &modelRequest, warehouseStoreRelation)
					if err != nil {
						wg.Done()
						logger.Error("库存同步失败：", GoodsStock.ZilongId, err.Error())
					}
					if icOut.Code == 200 {
						wg.Done()
						successCount++
					}
				}(l, warehouseStoreRelationMap[l.ZilongId])
			}
			wg.Wait()
			request.GoodsStock = request.GoodsStock[l:]
			if len(request.GoodsStock) == 0 {
				break
			}
		}
		_, err := conn.Exec("update stock_task set status=1,last_date='" + time.Now().Format("2006-01-02 15:04:05") + "' where id in (" + strings.Join(stockIds, ",") + ")")
		if err != nil {
			logger.Error("更新stock_task状态错误" + err.Error())
		}
		//数据处理成功了，删除处理失败
		logger.Info(" requestGoodsStock为", oldCount, " successCount", successCount, " ID：", strings.Join(stockIds, ","))
	}
}

//根据sku、财务编码、spu、子龙货号查询商品信息以及库存(门店仓)
func (c *StockService) GetZLStockInfo(redisConn *redis.Client, model models.Warehouse, skuIds []int32) map[int32]int32 {
	//初始化库存信息
	var skuStock = make(map[int32]int32, 0)
	for _, v := range skuIds {
		skuStock[v] = 0
	}
	//根据仓库编号获取子龙id
	ZlId := redisConn.HGet("store:relation:dctozl", model.Code).Val()
	url := fmt.Sprintf("%s%s", config.GetString("bj-scrm-url"), "inventory/orderCenter/queryInventory")
	var params dtos.GetZLStockInfoRequest
	params.Criteria.IsAll = 1
	params.Criteria.OrgID, _ = strconv.Atoi(ZlId)
	if params.Criteria.OrgID == 0 {
		return skuStock
	}
	var eng = GetDCProductMysqlConn()
	defer eng.Close()
	var skuThirds []models.SkuThird
	err := eng.In("sku_id", skuIds).And("erp_id=4").Find(&skuThirds)
	if err != nil {
		logger.Error("查询商品SKUID对应的子龙货号失败：", err)
		return skuStock
	}
	var thirdSkuToSkuIdMap = make(map[string]models.SkuThird, 0)
	for _, v := range skuThirds {
		thirdSkuToSkuIdMap[v.ThirdSkuId] = v
		params.Criteria.ItemCodeList = append(params.Criteria.ItemCodeList, v.ThirdSkuId)
	}
	dataJson, err := json.Marshal(params)
	if err != nil {
		return skuStock
	}
	logger.Info("queryInventory接口参数：", string(dataJson))
	resData, err := utils.HttpPostToBJ(url, dataJson, "")
	logger.Info("queryInventory返回结果=", string(resData))
	if err != nil {
		return skuStock
	}
	var baseRes = dtos.GetZLStockInfoResponse{}
	err = json.Unmarshal(resData, &baseRes)

	for _, v := range baseRes.Result {
		if _, ok := thirdSkuToSkuIdMap[v.GoodsID]; ok {
			thirdSku := thirdSkuToSkuIdMap[v.GoodsID]
			skuStock[thirdSku.SkuId] = int32(v.Stock)
		}
	}
	return skuStock
}
