package services

import (
	"_/dtos"
	"_/models"
	"_/proto/ic"
	"_/proto/pc"
	"_/utils"
	"context"
	"encoding/json"
	"fmt"
	"github.com/go-redis/redis"
	"reflect"
	"testing"

	"github.com/maybgit/glog"
)

func TestPublicMq(t *testing.T) {
	tests := []struct {
		name string
	}{
		// TODO: Add test cases.
		{"处理未推送的核销状态"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			//PublicMq()
		})
	}
}

func TestStockService_FreezeStock(t1 *testing.T) {
	type args struct {
		ctx context.Context
		in  *ic.FreezeRequest
	}
	var freezeRequest = ic.FreezeRequest{
		Source:  2,
		OrderId: "40080882test",
	}
	var orderGoodsInfo = ic.OrderGoodsInfo{
		GoodsId:     "1519525409",
		Number:      1,
		WarehouseId: 887,
	}
	freezeRequest.GoodsList = append(freezeRequest.GoodsList, &orderGoodsInfo)
	//var orderGoodsInfo1 = ic.OrderGoodsInfo{
	//	GoodsId:     "100013",
	//	Number:      3,
	//	WarehouseId: 880,
	//}
	//freezeRequest.GoodsList = append(freezeRequest.GoodsList, &orderGoodsInfo1)

	tests := []struct {
		name    string
		args    args
		want    *ic.FreezeResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "冻结库存",
			args: args{
				ctx: nil,
				in:  &freezeRequest,
			}},
	}
	for _, tt := range tests {
		t1.Run(tt.name, func(t1 *testing.T) {
			t := StockService{}
			got, err := t.FreezeStock(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t1.Errorf("FreezeStock() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t1.Errorf("FreezeStock() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestImportStock(t *testing.T) {
	//engine := GetMysqlConn()
	//var mapGoodsQty []dtos.GoodsStock
	//engine.Table("sheet1").Find(&mapGoodsQty)
	//println(len(mapGoodsQty))
	//utils.RedisDealKeysByTTL(fmt.Sprintf("Freeze:*:*:%d", 69))
	param := pc.StockNoticeRequest{}
	stockStore := pc.StockStore{}

	storeSku := &pc.StoreSku{
		SkuId: int32(1532751507),
		Stock: 1,
	}
	stockStore.Skus = append(stockStore.Skus, storeSku)

	storeSku1 := &pc.StoreSku{
		SkuId: int32(1511596409),
		Stock: 1,
	}
	stockStore.Skus = append(stockStore.Skus, storeSku1)

	storeSku2 := &pc.StoreSku{
		SkuId: int32(1511597409),
		Stock: 1,
	}
	stockStore.Skus = append(stockStore.Skus, storeSku2)

	storeSku3 := &pc.StoreSku{
		SkuId: int32(1511598409),
		Stock: 1,
	}
	stockStore.Skus = append(stockStore.Skus, storeSku3)

	param.StockInfo = append(param.StockInfo, &stockStore)
	groupProductStockNotice(param)
}

func TestFreedStock(t *testing.T) {
	type args struct {
		orderId string
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
		{"释放库存", args{orderId: "111"}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			//FreedStock("SSS111122222")
			c := &StockService{}
			c.RunWarehouseRedisByManual(nil, nil)
		})
	}
}

func TestStockService_GetStockInfo(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *ic.GetStockInfoRequest
	}
	var in ic.GetStockInfoRequest
	in.Source = 2
	in.IsNeedPull = 0

	var model ic.ProductsInfo

	model.Type = 2
	model.SkuId = 1043547001
	model.IsAllVirtual = 0
	model.Stock = 0
	model.FinanceCode = append(model.FinanceCode, "CX0013")
	in.Stockwarehouse = append(in.Stockwarehouse, 2816)
	//goods_list= append(goods_list, model)
	in.ProductsInfo = append(in.ProductsInfo, &model)

	dd, _ := json.Marshal(&in)
	fmt.Println(string(dd))
	tests := []struct {
		name    string
		args    args
		want    *ic.GetStockInfoResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "GetStockByProductId",
			args: args{
				ctx: nil,
				in:  &in,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &StockService{}

			got, err := c.GetStockInfo(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetStockInfoV5() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetStockInfoV5() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestStockService_FreedStock(t1 *testing.T) {
	type args struct {
		ctx context.Context
		in  *ic.FreedStockRequest
	}
	var model ic.FreedStockRequest
	model.OrderId = "1111111112222"
	model.Source = 1
	var orderGoodsInfo = ic.OrderGoodsInfo{
		GoodsId:     "100013",
		Number:      1,
		WarehouseId: 2,
	}
	model.GoodsList = append(model.GoodsList, &orderGoodsInfo)
	var orderGoodsInfo1 = ic.OrderGoodsInfo{
		GoodsId:     "100014",
		Number:      1,
		WarehouseId: 2,
	}
	model.GoodsList = append(model.GoodsList, &orderGoodsInfo1)
	/*	var orderGoodsInfo2 = ic.OrderGoodsInfo{
			GoodsId:     "100014",
			Number:      2,
			WarehouseId: 2,
		}
		model.GoodsList = append(model.GoodsList, &orderGoodsInfo2)*/

	tests := []struct {
		name    string
		args    args
		want    *ic.FreedStockResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "GetStockByProductId",
			args: args{
				ctx: nil,
				in:  &model,
			},
		},
	}
	for _, tt := range tests {
		t1.Run(tt.name, func(t1 *testing.T) {
			t := StockService{}
			got, err := t.FreedStock(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t1.Errorf("FreedStock() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t1.Errorf("FreedStock() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestStockService_RunStockByManual(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *ic.OmsSyncSotckResponse
	}
	tests := []struct {
		name    string
		t       StockService
		args    args
		want    *ic.OmsSyncSotckResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "前置仓手动掉库存"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			//ff := StockService{}
			app := ic.OmsSyncSotckResponse{}
			app.Message = "20"
			queue := "dc_sz_stock_update"
			request := `{"Code":"","Isfinish":"0","Orderid":"","Goodslist":null,"IsOMS":1,"data":[{"goodslist":[{"spu":"102455","sku":"104987","stock":49,"allstock":0,"thirdsku":"PYRMJ010"}],"warehouse_code":"JD01"}]}`
			glog.Info(fmt.Sprintf("订阅队列为%s的信息内容：%s", queue, request))
			//解析内容
			var updateGoodsStock dtos.UpdateGoodsStock
			if err := json.Unmarshal([]byte(request), &updateGoodsStock); err != nil {
				//glog.Error(err)
				panic(err)
			}
			//如果是OMS推送过来的库存更新数据
			if updateGoodsStock.IsOMS == 1 {
				conn := GetMysqlConn()
				defer conn.Close()
				//循环加入库存信息
				for _, warehouse := range updateGoodsStock.Data {
					//查询仓库信息
					warehouseMode := models.Warehouse{}
					_, _ = conn.Where("code=?", warehouse.WarehouseCode).And("status=1").Get(&warehouseMode)
					var mapGoodsQty []dtos.GoodsStock
					var goodsIds []string
					//循环加入库存信息
					for _, stockDetail := range warehouse.Goodslist {
						//库存为零则初始化数据
						if stockDetail.Stock < 0 {
							stockDetail.Stock = 0
						}
						var _mapQty = dtos.GoodsStock{
							GoodsId: stockDetail.Sku,
							Stock:   stockDetail.Stock,
							SpuId:   stockDetail.Spu,
						}
						mapGoodsQty = append(mapGoodsQty, _mapQty)
						goodsIds = append(goodsIds, stockDetail.Sku)
					}
					writeStock(warehouseMode, goodsIds, mapGoodsQty)
				}
			}

			//got, err := ff.RunStockByManual(context.Background(), &app)
			//if (err != nil) != tt.wantErr {
			//	t.Errorf("StockService.RunStockByManual() error = %v, wantErr %v", err, tt.wantErr)
			//	return
			//}
			//if !reflect.DeepEqual(got, tt.want) {
			//	t.Errorf("StockService.RunStockByManual() = %v, want %v", got, tt.want)
			//}
		})
	}
}

func TestStockService_makeSureStockCache(t *testing.T) {
	redisConn := utils.ConnectClusterRedis()
	defer redisConn.Close()
	type args struct {
		conn        *redis.Client
		warehouseId string
		list        []*ic.OrderGoodsInfo
	}
	tests := []struct {
		name string
		tr   StockService
		args args
	}{
		{
			name: "测试确保库存缓存",
			tr:   StockService{},
			args: args{
				conn:        redisConn,
				warehouseId: "1437",
				list: []*ic.OrderGoodsInfo{
					{
						GoodsId: "1023549001",
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.tr.makeSureStockCache(tt.args.conn, tt.args.warehouseId, tt.args.list)
		})
	}
}

func TestStockService_QueryStock(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *ic.QueryStockReq
	}
	tests := []struct {
		name    string
		args    args
		wantOut *ic.QueryStockRes
		wantErr bool
	}{
		{
			name: "",
			args: args{in: &ic.QueryStockReq{
				ChannelId: 5,
				SkuIds:    []int32{1051215099, 1034750001, 1034894099},
			}},
		},
		{
			name: "",
			args: args{in: &ic.QueryStockReq{
				ChannelId: 5,
				SkuIds:    []int32{},
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &StockService{}
			gotOut, err := c.QueryStock(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("QueryStock() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("QueryStock() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}
