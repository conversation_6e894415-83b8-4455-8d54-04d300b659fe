package models

import (
	"errors"
	"github.com/go-xorm/xorm"
	"time"
)

type WarehouseGoods struct {
	Id          int       `xorm:"not null pk autoincr comment('自增') INT(11)"`
	Goodsid     string    `xorm:"not null default ''0'' comment('商品id') VARCHAR(40)"`
	Stock       int       `xorm:"not null default 0 comment('商品库存') INT(11)"`
	Lastdate    time.Time `xorm:"not null default 'current_timestamp()' comment('最后更新时间') TIMESTAMP updated"`
	WarehouseId int       `xorm:"not null default 0 comment('仓库id') INT(11)"`
}

// StockIncr 库存自增并返回当前库存
// 会话一致性，方法仅在事务中有效
func (wg WarehouseGoods) StockIncr(session *xorm.Session, incr int) (stock int, err error) {
	if _, err = session.Exec("update dc_order.warehouse_goods set stock=stock+? where id=?", incr, wg.Id); err != nil {
		return 0, errors.New("自增库存 " + err.Error())
	}
	if _, err = session.Table("dc_order.warehouse_goods").Where("id = ?", wg.Id).Select("stock").Get(&stock); err != nil {
		return 0, errors.New("自增库存后查询 " + err.Error())
	}
	return
}
