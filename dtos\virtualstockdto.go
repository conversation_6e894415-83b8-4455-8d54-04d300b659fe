package dtos

type GoodsStock struct {
	SpuId   string //商品库的spuid
	GoodsId string //商品库的skuid
	Stock   int
}

type A8VirtualGoodsInfo struct {
	TypeId       string
	UserCode     string
	FullName     string
	EntryCode    string
	Standard     string
	Type         string
	Area         string
	Unit1        string
	Unit2        string
	UnitRate1    string
	SonNum       string
	Leveal       string
	Parid        string
	Qty          string
	Factqty      string
	LendQty      string
	BorrowQty    string
	BuyOrderQty  string
	SaleOrderQty string
	SaleCanQty   string
	RQty         string
	CQty         string
	KFHQty       string
	Saleqty      string
	UnitName     string
	BaseUnitName string
	UnitRate     string
	Price1       string
	BuyReferQty  string
	SaleReferQty string
	LimitBuyQty  string
	LimitSaleQty string
	Recprice     string
	VirtualQty   string
	CostTotal    string
	Price        string
}

//获取库存接口
type SignaureParam struct {
	Appkey     string `json:"appkey"`         // 请求管易那边的Appkey
	Sessionkey string `json:"sessionkey"`     // 请求管易那边的Sessionkey
	Method     string `json:"method"`         // 请求方式   赋值通常为管易接口链接
	Sign       string `json:"sign,omitempty"` // 加密后的请求Sign   类似于Token
}
type GYGoodsStockParam struct {
	SignaureParam         //  主传递
	Page_no        int    `json:"page_no,omitempty"`        // 页码
	Page_size      int    `json:"page_size,omitempty"`      // 每页条数
	Start_date     string `json:"start_date,omitempty"`     // 开始时间
	End_date       string `json:"end_date,omitempty"`       // 结束时间
	Barcode        string `json:"barcode,omitempty"`        // 商品条码
	Warehouse_code string `json:"warehouse_code,omitempty"` // 仓库代码
	Cancel         int    `json:"cancel,omitempty"`         // 是否返回停用的库存记录 0:不返回停用库存记录 1：返回停用的库存记录 默认1
	Item_code      string `json:"item_code,omitempty"`      // 商品代码
	Item_sku_code  string `json:"item_sku_code,omitempty"`  // 规格代码
}

// 返回公用 struct
type GYBaseResult struct {
	Success       bool   `json:"success,omitempty"`       // 返回是否成功
	ErrorCode     string `json:"errorCode,omitempty"`     //错误代码
	SubErrorCode  string `json:"subErrorCode,omitempty"`  // 子错误代码
	ErrorDesc     string `json:"errorDesc,omitempty"`     // 错误描述
	SubErrorDesc  string `json:"subErrorDesc,omitempty"`  // 子错误描述
	RequestMethod string `json:"requestMethod,omitempty"` // 请求方式
}

//获取库存接口 返回值
type GYStockResult struct {
	GYBaseResult
	Stocks []GYStock `json:"stocks,omitempty"` // 库存记录列表
	Total  int       `json:"total,omitempty"`  // 总记录数
}

//获取库存接口
type GYStock struct {
	Barcode        string  `json:"barcode,omitempty"`        //商品条码
	Del            bool    `json:"del,omitempty"`            // 是否停用
	Qty            float64 `json:"qty,omitempty"`            // 库存数量
	Warehouse_code string  `json:"warehouse_code,omitempty"` // 仓库代码
	Item_code      string  `json:"item_code,omitempty"`      // 商品代码
	Sku_code       string  `json:"sku_code,omitempty"`       // 商品规格代码
	Salable_qty    float64 `json:"salable_qty,omitempty"`    // 可销售数
	Road_qty       float64 `json:"road_qty,omitempty"`       // 在途数
	Pick_qty       float64 `json:"pick_qty,omitempty"`       // 可配数
	Item_name      string  `json:"item_name,omitempty"`      // 商品名称
	Item_sku_name  string  `json:"item_sku_name,omitempty"`  // 商品规格名称
	Warehouse_name string  `json:"warehouse_name,omitempty"` // 仓库名称
}
