package dtos

type WarehouseStoreRelation struct {
	ZilongId    string //子龙的Id
	FanceCode   string //财务code
	WarehouseId int    //仓库id
	Code        string
	Category    int
	Status      int
}

type RetailSkuStock struct {
	// APP方门店id--美团id
	App_poi_code string `json:"app_poi_code" form:"app_poi_code" query:"app_poi_code"`
	// 商品sku库存集合的json数据
	Food_data []FoodData `json:"food_data" form:"food_data" query:"food_data"`
}
type FoodData struct {
	// app_food_code(必填项),APP方商品id
	App_food_code string `json:"app_food_code" form:"app_food_code" query:"app_food_code"`
	// skus(必填项)，商品sku信息集合的json数组
	Skus []Skus `json:"skus" form:"skus" query:"skus"`
}

type Skus struct {
	// sku_id(必填项)，是sku唯一标识码
	Sku_id string `json:"sku_id" form:"sku_id" query:"sku_id"`
	// stock(必填项)，为sku的库存，传非负整数，若传"*"表示库存无限
	Stock string `json:"stock" form:"stock" query:"stock"`
}

//门店仓与前置仓信息表
type WarehouseInfoDto struct {
	//门店仓id
	Id int
	//门店财务编码
	Code string
	//门店仓状态
	Status int
	//前置仓id
	WarehouseId int
	//前置仓的状态
	Hstate int
}

//请求参数
type GetZLStockInfoRequest struct {
	Criteria struct {
		IsAll        int      `json:"isAll"`
		OrgID        int      `json:"orgId"`
		ItemCodeList []string `json:"itemCodeList"`
	} `json:"criteria"`
}

//响应参数
type GetZLStockInfoResponse struct {
	Result []struct {
		GoodsID string `json:"goodsId"`
		Stock   int    `json:"stock"`
		OrgID   int    `json:"orgId"`
	} `json:"result"`
	Message       interface{} `json:"message"`
	SystemError   interface{} `json:"systemError"`
	BusinessError interface{} `json:"businessError"`
	StatusCode    int         `json:"statusCode"`
	Extensions    struct {
	} `json:"extensions"`
	Success bool `json:"success"`
}

type ResultStock struct {
	List         []StockGoods `json:"list"`
	SerialNumber string       `json:"serial_number"`
}

type ResultStock1 struct {
	List []StockGoods `json:"list"`
}

type StockGoods struct {
	ZilongID  string      `json:"zilong_id"`
	GoodsList []GoodsList `json:"GoodsList"`
}

type GoodsList struct {
	GoodsID string `json:"GoodsID"`
	Stock   int    `json:"Stock"`
}
