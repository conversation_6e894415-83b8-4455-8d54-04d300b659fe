package utils

import (
	"_/proto/dc"
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-redis/redis"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
)

// 加载渠道仓库缓存数据
func LoadChannelWarehouseCache(conn *redis.Client, financeCode string, channelId int) *WarehouseRelationShop {
	val := conn.HGet("mt_shop_bind_warehouse", fmt.Sprintf("%s:%d", financeCode, channelId)).Val()
	if val != "" {
		obj := &WarehouseRelationShop{}
		err := json.Unmarshal([]byte(val), obj)
		if err == nil {
			return obj
		}
		glog.Errorf("LoadChannelWarehouseCache 反序列化失败:%+v %s %d %s", err, financeCode, channelId, val)
	}

	client := dc.GetDcDispatchClient()
	defer client.Close()
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*30)
	defer cancel()
	resp, err := client.RPC.ShopBindInfoListByShopId(ctx, &dc.ShopBindInfoByShopIdRequest{
		ShopId: []string{
			financeCode,
		},
		ChannelId: int32(channelId),
	})
	if err != nil {
		glog.Errorf("LoadChannelWarehouseCache RPC查询渠道仓库异常:%+v %s %d %s", err, financeCode, channelId, val)
		return nil
	}
	glog.Infof("LoadChannelWarehouseCache RPC查询渠道仓库结果:%s %s %d", kit.JsonEncode(resp), financeCode, channelId)

	if len(resp.Info) == 0 {
		glog.Warningf("LoadChannelWarehouseCache 当前渠道未绑定渠道仓库:%s %d", financeCode, channelId)
		return nil
	}
	info := resp.Info[0]
	return &WarehouseRelationShop{
		WarehouseId:   info.WarehouseId,
		WarehouseName: info.WarehouseName,
		Code:          info.Code,
		ShopId:        info.ShopId,
		ShopName:      info.ShopName,
		ChannelId:     info.ChannelId,
		Category:      info.Category,
	}
}

type WarehouseRelationShop struct {
	WarehouseId   int32     `json:"warehouse_id"`
	WarehouseName string    `json:"warehouse_name"`
	Code          string    `json:"code"`
	ShopId        string    `json:"shop_id"`
	ShopName      string    `json:"shop_name"`
	ChannelId     int32     `json:"channel_id"`
	Category      int32     `json:"category"`
	CreateTime    time.Time `json:"create_time"`
}
