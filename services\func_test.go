package services

import (
	"_/dtos"
	"_/models"
	"testing"
)

func Test_writeStock(t *testing.T) {
	type args struct {
		warehouse  models.Warehouse
		goodsId    []string
		stocks     []dtos.GoodsStock
		isIncrease []bool
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			args: args{
				warehouse: models.Warehouse{Id: 1294},
				goodsId:   []string{"1000118001", "1031500001", "1020751001"},
				stocks: []dtos.GoodsStock{
					{
						SpuId:   "1",
						GoodsId: "1000118001",
						Stock:   1,
					}, {
						SpuId:   "2",
						GoodsId: "1031500001",
						Stock:   1,
					}, {
						SpuId:   "3",
						GoodsId: "1020751001",
						Stock:   10,
					},
				},
				// isIncrease: []bool{true},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := writeStock(tt.args.warehouse, tt.args.goodsId, tt.args.stocks, tt.args.isIncrease...)
			if (err != nil) != tt.wantErr {
				t.Errorf("writeStock() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
