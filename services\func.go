package services

import (
	"_/dtos"
	"_/models"
	"_/proto/sh"
	"_/utils"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/go-redis/redis"
	"github.com/go-xorm/xorm"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
	"strconv"
	"strings"
	"time"
)

// 写库存处理器
type writeStockHandler struct {
	Warehouse      models.Warehouse
	Stocks         []dtos.GoodsStock
	IsIncrease     bool     // 是否是增量处理
	SkuIds         []string // 关联商品skuIds
	Notices        []string
	HasStockUpdate []string // 有无库存变动
	IsDsc          bool     // 是不是电商仓
	Db             *xorm.Engine
	Redis          *redis.Client
}

// 写库存
// 业务逻辑：先更新传进来的商品信息对应的库存（redis也要清理掉)，再计算在途库存
func writeStock(warehouse models.Warehouse, goodsId []string, stocks []dtos.GoodsStock, isIncrease ...bool) (err error) {
	if len(stocks) == 0 {
		return
	}

	h, err := initWriteStockHandler(warehouse, stocks, len(isIncrease) > 0 && isIncrease[0] == true)

	// 记录日志及清理工作
	defer func() {
		_ = h.Db.Close()
		_ = h.Redis.Close()
		glog.Info("库存 writeStock 入参：", kit.JsonEncode(map[string]interface{}{
			"warehouse": warehouse, "goodsId": goodsId, "stocks": stocks, "isIncrease": isIncrease,
		}), "，警告：", h.Notices, err, "，错误：", err)
	}()

	if err != nil || len(h.Stocks) < 1 {
		return
	}
	lockKey := fmt.Sprintf("inventory-center:write-stock-lock:%d", h.Warehouse.Id)
	// 并发可能导致数据错乱，加个阻塞锁
	if err = h.acquireLock(lockKey); err != nil {
		return
	} else {
		// 不考虑lua脚本校验锁值匹配
		defer h.Redis.Del(lockKey)
	}

	// 第一步 刷新数据库库存
	if err = h.refreshWarehouseGoods(goodsId); err != nil {
		return
	}
	// 第二步 清理缓存库存
	h.deleteStockCache()
	// 第三步 同步第三方库存
	h.syncStock()

	return
}

// 初始化处理器
func initWriteStockHandler(warehouse models.Warehouse, stocks []dtos.GoodsStock, isIncrease bool) (h *writeStockHandler, err error) {
	h = &writeStockHandler{
		Warehouse:  warehouse,
		IsIncrease: isIncrease,
		Db:         GetMysqlConn(),
		Redis:      utils.ConnectClusterRedis(),
	}
	for _, stock := range stocks {
		if len(stock.GoodsId) < 1 {
			continue
		}
		h.Stocks = append(h.Stocks, stock)
		h.SkuIds = append(h.SkuIds, stock.GoodsId)
	}

	// 部分调用仓库只传了id
	if h.Warehouse.Category == 0 {
		if has, err := h.Db.Table("dc_dispatch.warehouse").Select("id,category").
			Where("id = ?", h.Warehouse.Id).Get(&h.Warehouse); err != nil {
			return nil, errors.New("反查仓库信息 " + err.Error())
		} else if !has {
			return nil, errors.New("仓库未找到")
		}
	}

	code := cast.ToString(h.Warehouse.Id)
	// 所有的电商仓库
	for _, wid := range strings.Split(config.GetString("DSStock"), ",") {
		if wid == code {
			h.IsDsc = true
			break
		}
	}

	return
}

// 阻塞获得锁
func (h *writeStockHandler) acquireLock(lockKey string) (err error) {
	// 最多等待30s
	for i := 0; i < 300; i++ {
		lock, err := h.Redis.SetNX(lockKey, 1, 1*time.Minute).Result()
		if err != nil {
			return errors.New("获取锁出错 " + err.Error())
		}
		if !lock {
			// 100ms重试一次
			time.Sleep(100 * time.Millisecond)
			continue
		}
		return nil
	}

	return errors.New("获取锁超时 " + err.Error())
}

// 第一步 刷新数据库库存
func (h *writeStockHandler) refreshWarehouseGoods(skuIds []string) (err error) {
	session := h.Db.NewSession()
	defer session.Close()
	_ = session.Begin()

	var wgs, newWgs []*models.WarehouseGoods
	if err = h.Db.Table("dc_order.warehouse_goods").Where("warehouse_id = ?", h.Warehouse.Id).
		Select("id,goodsid,stock").In("goodsid", h.SkuIds).Find(&wgs); err != nil {
		return
	}

	wgMaps := make(map[string]*models.WarehouseGoods, len(wgs))
	for _, wg := range wgs {
		wgMaps[wg.Goodsid] = wg
	}

	for k, stock := range h.Stocks {
		if wg, has := wgMaps[stock.GoodsId]; has {
			// 如果是增量同步
			if h.IsIncrease {
				if stock.Stock == 0 {
					h.Stocks[k].Stock = wg.Stock
				} else {
					// 从无到有
					if wg.Stock == 0 {
						h.HasStockUpdate = append(h.HasStockUpdate, wg.Goodsid)
					}
					if h.Stocks[k].Stock, err = wg.StockIncr(session, stock.Stock); err != nil {
						return
					}
				}
			} else {
				if wg.Stock != stock.Stock {
					// 从无到有、从有到无
					if wg.Stock == 0 || stock.Stock == 0 {
						h.HasStockUpdate = append(h.HasStockUpdate, wg.Goodsid)
					}
					if _, err = session.Exec("update dc_order.warehouse_goods set stock = ? where id = ?", stock.Stock, wg.Id); err != nil {
						return errors.New("更新库存 " + err.Error())
					}
				}
			}
		} else {
			if h.IsIncrease && stock.Stock == 0 {
				continue
			}
			newWgs = append(newWgs, &models.WarehouseGoods{
				Goodsid:     stock.GoodsId,
				Stock:       stock.Stock,
				WarehouseId: h.Warehouse.Id,
			})

			// 从无到有
			h.HasStockUpdate = append(h.HasStockUpdate, stock.GoodsId)
		}
	}

	// 沿用旧逻辑，如果有传goodsid则需要过滤，否则按传进来的仓库id去处理
	if !h.IsIncrease {
		q := session.Table("dc_order.warehouse_goods").Where("warehouse_id = ?", h.Warehouse.Id).NotIn("goodsid", h.SkuIds)
		if len(skuIds) > 0 {
			q.In("goodsid", skuIds)
		}
		if _, err = q.Delete(new(models.WarehouseGoods)); err != nil {
			return errors.New("删除过滤数据 " + err.Error())
		}
	}

	if len(newWgs) > 0 {
		if _, err = session.Table("dc_order.warehouse_goods").Insert(newWgs); err != nil {
			return errors.New("插入库存商品 " + err.Error())
		}
	}

	return session.Commit()
}

// 第二步 清理缓存库存
func (h *writeStockHandler) deleteStockCache() {
	code := cast.ToString(h.Warehouse.Id)

	if h.IsDsc {
		code = "DSC001"
	}

	pipe := h.Redis.Pipeline()
	for _, v := range h.Stocks {
		pipe.HDel(fmt.Sprintf("stock:%s", v.GoodsId), code)
	}
	if _, err := pipe.Exec(); err != nil {
		h.Notices = append(h.Notices, "删除缓存库存 "+err.Error())
	}

	return
}

// 第三步 同步第三方库存
// 任意错误都不退出，因为已经更新库存表了，需要记录日志
func (h *writeStockHandler) syncStock() {
	// 电商仓同步有无库存到es
	if h.IsDsc && len(h.HasStockUpdate) > 0 {
		client := sh.GetUpetCenterClient()
		if res, err := client.PS.UpdateGoodsToEs(client.Ctx, &sh.GoodsToEsUpdateRequest{
			Ids: strings.Join(h.HasStockUpdate, ","),
		}); err != nil {
			h.Notices = append(h.Notices, "电商调用UpdateGoodsToEs出错 "+err.Error())
		} else if res.Code != 200 {
			h.Notices = append(h.Notices, "电商调用UpdateGoodsToEs出错 "+res.Message)
		}
	}

	// 仅 门店仓、前置仓、前置虚拟仓 需要额外处理
	if h.Warehouse.Category < 3 || h.Warehouse.Category > 5 {
		return
	}

	fss, err := models.QueryFreezeStocks(h.Db, h.Warehouse.Id, h.SkuIds)
	if err != nil {
		h.Notices = append(h.Notices, err.Error())
	}

	var foodData []dtos.FoodData
	for _, v := range h.Stocks {
		// 实际库存需要减掉冻结
		stock := v.Stock - fss[v.GoodsId]
		if stock < 0 {
			stock = 0
		}
		foodData = append(foodData, dtos.FoodData{
			App_food_code: v.SpuId,
			Skus: []dtos.Skus{{
				Sku_id: v.GoodsId,
				Stock:  strconv.Itoa(stock),
			}},
		})
	}
	if len(foodData) < 1 {
		return
	}

	h.insertSyncTaskByFoodData(foodData)

	return
}

// 插入更新数据到mqInfo任务
func (h *writeStockHandler) insertSyncTaskByFoodData(foodData []dtos.FoodData) {
	// 分组可以减少编码json处理次数
	type RelationGroup struct {
		ChannelIds string `json:"channel_ids"`
		ShopId     string `json:"shop_id"`
	}
	var relations []*RelationGroup

	if err := h.Db.Table("dc_dispatch.warehouse_relation_shop").Where("warehouse_id = ?", h.Warehouse.Id).
		Select("shop_id,group_concat(channel_id) as channel_ids").GroupBy("shop_id").Find(&relations); err != nil {
		h.Notices = append(h.Notices, "查询仓库门店关系 "+err.Error())
		return
	} else if len(relations) < 1 {
		return
	}

	var mqInfos []models.MqInfo
	// 每页最多商品数量
	pageSize, total := 100, len(foodData)

	for i := 0; i < total; i = i + pageSize {
		end := i + pageSize
		if end > total {
			end = total
		}

		// 按仓库更新有无库存
		hasStockContent, _ := json.Marshal(dtos.RetailSkuStock{
			App_poi_code: fmt.Sprintf("#%d", h.Warehouse.Id),
			Food_data:    foodData[i:end],
		})
		mqInfos = append(mqInfos, models.MqInfo{
			Exchange: "datacenter",
			Quene:    "dc_sz_stock_mq_has_stock",
			Content:  string(hasStockContent),
		})

		// 遍历当前仓库关联的门店渠道分别推送库存
		for _, relation := range relations {
			content, _ := json.Marshal(dtos.RetailSkuStock{
				App_poi_code: relation.ShopId,
				Food_data:    foodData[i:end],
			})

			mqInfo := models.MqInfo{
				Exchange: "datacenter",
				Content:  string(content),
			}

			for _, channelId := range strings.Split(relation.ChannelIds, ",") {
				switch channelId {
				case "2": // 美团
					mqInfo.Quene = "dc_sz_stock_mq"
				case "3": // 饿了么
					mqInfo.Quene = "dc_sz_stock_mq_elm"
				case "4": // 京东到家
					mqInfo.Quene = "dc_sz_stock_mq_jddj"
				default:
					continue
				}
				mqInfos = append(mqInfos, mqInfo)
			}
		}
	}

	if len(mqInfos) > 0 {
		if err := models.SliceInsertMqInfo(h.Db, mqInfos); err != nil {
			h.Notices = append(h.Notices, err.Error())
		}
	}

	return
}
