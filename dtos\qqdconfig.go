package dtos

//全渠道请求API需要的参数
type QqdConfig struct {
	AppkeyCon    string
	AppsecretCon string
	Token        string
	ApiUrl       string
}

//全渠道获取库存请求参数
type StockGetRequest struct {
	Page         int    `json:"page"`
	Pagesize     int    `json:"pagesize"`
	Goodscode    string `json:"goodscode"`
	Skucode      string `json:"skucode"`
	Whscode      string `json:"whscode"`
	Iscontainwhs bool   `json:"iscontainwhs"`
}

//全渠道获取库存返回
type StockGetResponse struct {
	Code    int           `json:"code"`
	Message string        `json:"message"`
	Total   int           `json:"total"`
	Stocks  []StocksDetil `json:"stocks"`
}
type StocksDetil struct {
	//第三方skuId
	Goodscode string  `json:"goodscode"`
	Goodsname string  `json:"goodsname"`
	Ensalenum float64 `json:"ensalenum"`
	Whscode   string  `json:"whscode"`
	Whsname   string  `json:"whsname"`

	Skuid     string  `json:"skuid"`
	Goodsid   string  `json:"goodsid"`
	Skuname   string  `json:"skuname"`
	Qty       float64 `json:"qty"`
	Enablenum float64 `json:"enablenum"`
}
