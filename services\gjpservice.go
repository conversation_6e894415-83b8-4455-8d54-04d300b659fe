package services

import (
	"_/dtos"
	"_/models"
	"_/proto/ic"
	"_/proto/pc"
	"_/utils"
	"context"
	"encoding/json"
	"fmt"
	"github.com/go-redis/redis"
	"github.com/maybgit/glog"
	logger "github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
	"strconv"
	"strings"
	"time"
)

type GJPService struct {
	DBService
}

//获取前置仓库存信息-新版本
func SyncA8VirtualGoodsQtyForQZC() {
	// 已切oms
}

func (t StockService) IsOmsWarehouseCode(ctx context.Context, in *ic.IsOmsWarehouseVo) (*ic.IsOmsWarehouseResp, error) {
	resp := ic.IsOmsWarehouseResp{Code: 200, IsOms: false}
	//查询仓库白名单
	db := GetMysqlConn()
	defer db.Close()

	if isOms, err := db.Table("dc_dispatch.warehouse").Where("code = ? and category in (4,5)", in.Warehouse).Exist(); err != nil {
		glog.Info("IsOmsWarehouseCode获取配置参数出错：", err.Error())
	} else {
		resp.IsOms = isOms
	}

	glog.Info("IsOmsWarehouseCode配置返回：", kit.JsonEncode(in), " 返回值：", kit.JsonEncode(resp))
	return &resp, nil
}

//获取前置仓库存信息-新版本
func SyncA8VirtualGoodsQtyForQZCVirtual() {
	// 已切oms
}

//根据仓库id查询商品库存 ，自动同步到数据库中
// @param goodsCode string a8货号也就是sku_third表里的third_sku_id,多个用英文逗号隔开
// @param goodsId []string 平台的商品skuid
// @return result  当goodsCode存在的时候， 返回前100条数据
func (s *GJPService) GetA8VirtualGoodsQtyForQZC(warehouse models.Warehouse, goodsCode string, goodsId []string) (result *dtos.StockGetResponse) {
	logStr := "activity-center-GetA8VirtualGoodsQtyForQZC-eva-入参=warehouse=" + kit.JsonEncode(warehouse) + ",goodsCode=" + goodsCode + ",goodsId=" + kit.JsonEncode(goodsId)
	logger.Info(logStr)

	defer func() {
		if err := recover(); err != nil {
			logger.Error("GJPService.GetA8VirtualGoodsQtyBy", err)
		}
	}()

	mapGoodsQty := make([]dtos.GoodsStock, 0)
	var mysqlEngine = GetMysqlConn()
	defer mysqlEngine.Close()

	qqdCon := initQQD()
	params := new(dtos.StockGetRequest)
	params.Pagesize = 1
	params.Page = 1
	params.Iscontainwhs = true
	var goodsIdNew []string
	// 如果a8货号存在， 则直接使用， 如果不存在， 则通过平台的skuId获取第三方skuId
	if len(goodsCode) > 0 {
		goodsCodeSli := strings.Split(goodsCode, ",")
		logger.Info(logStr, ",数据记录=", kit.JsonEncode(goodsCodeSli))
		if len(goodsCodeSli) > 100 {
			goodsCodeSli = goodsCodeSli[:100]
		}

		params.Goodscode = strings.Join(goodsCodeSli, ",")

		//初始化 goodsId,writeStock需要删除对应的redis
		goodsId = make([]string, 0)

	} else {
		for _, v := range goodsId {
			params := pc.NewSkuThirdResponse{SkuId: v, ErpId: 2}
			//商品库的GRPC连接
			conn, ctx, client, cf := GetProductClient()
			//根据商品的子龙货号获取商品中心的spuid，skuid等信息
			icOut, err := client.QuerySkuThirdQQd(ctx, &params)
			conn.Close()
			cf()
			if err != nil {
				logger.Error("根据货号获取sku失败：", err)
				continue
			}
			goodsIdNew = append(goodsIdNew, icOut.Sku_Third)
		}
		if len(goodsIdNew) > 0 {
			params.Goodscode = strings.Join(goodsIdNew, ",") //处理商品库存信息
		}
	}

	//仓库代码
	params.Whscode = warehouse.Code
	signByte, _ := json.Marshal(params)
	signStr := string(signByte)
	//库存查询
	parStr := utils.Sign(signStr, "emall.stock.get", qqdCon.Token, qqdCon.AppkeyCon, qqdCon.AppsecretCon)
	bytesData, _ := json.Marshal(params)
	respBytes, err := utils.HttpPost(qqdCon.ApiUrl+parStr, bytesData, "application/json;charset=UTF-8", 0)
	logger.Info("lifasheng_check_qqd_stock-----获取库存请求参数：", string(bytesData), "。获取库存请求结果：", string(respBytes))
	if err != nil {
		logger.Error("获取库存请求参数：", string(bytesData), "。获取库存请求结果：", string(respBytes))
		panic(err)
	}
	retmes := new(dtos.StockGetResponse)
	err = json.Unmarshal(respBytes, retmes)
	if err != nil {
		logger.Error("无法格式化返回结果，错误原因：", err, "。数据集：", string(respBytes))
		panic(err)
	}
	//商品总条数
	qqdGoodsCount := retmes.Total
	//需要循环的次数
	forLen := 0
	//分页大小
	postSize := 100
	if qqdGoodsCount%postSize == 0 {
		forLen = qqdGoodsCount / postSize
	} else {
		forLen = qqdGoodsCount/postSize + 1
	}
	//设置每次查询100条
	params.Pagesize = postSize
	for i := 1; i <= forLen; i++ {
		time.Sleep(10 * time.Second)
		//设置查询当前页
		params.Page = i
		signByte, _ := json.Marshal(params)
		signStr := string(signByte)
		//库存查询
		parStr := utils.Sign(signStr, "emall.stock.get", qqdCon.Token, qqdCon.AppkeyCon, qqdCon.AppsecretCon)
		bytesData, _ := json.Marshal(params)
		respBytes, err := utils.HttpPost(qqdCon.ApiUrl+parStr, bytesData, "application/json;charset=UTF-8", 0)
		logger.Info("lifasheng_check_qqd_stock-----获取库存请求参数：", string(bytesData), "。获取库存请求结果：", string(respBytes))
		if err != nil {
			retryCount := 1
			//循环里面第一次请求失败后，重试三次
			for {
				//超过三次break
				if retryCount > 3 {
					break
				}
				respBytes, err = utils.HttpPost(qqdCon.ApiUrl+parStr, bytesData, "application/json;charset=UTF-8", int32(retryCount))
				//返回了最新的结果：break
				if respBytes != nil {
					break
				}
				retryCount++
			}
			//如果循环以后还是没有返回对应的数据
			if err != nil {
				logger.Error(fmt.Sprintf("拉取全渠道库存报错: %s;仓库编号：%s；获取库存请求参数：%s。", err.Error(), warehouse.Code, string(bytesData)), err)
				continue
			}

		}
		retmes := new(dtos.StockGetResponse)
		err = json.Unmarshal(respBytes, retmes)
		if err != nil {
			logger.Error(fmt.Sprintf("格式化全渠道库存报错: %s;仓库编号：%s。", err.Error(), warehouse.Code), err)
			continue
		}
		// 返回前100条数据
		if i == 1 {
			result = retmes
		}

		//循环加入库存信息
		for _, socksdetil := range retmes.Stocks {
			//库存为零则初始化数据
			if socksdetil.Ensalenum < 0 {
				socksdetil.Ensalenum = 0
			}

			params := pc.NewSkuThirdRequest{Sku_Third: socksdetil.Goodscode, ErpId: 2}
			//商品库的GRPC连接
			conn, ctx, client, cf := GetProductClient()
			//根据商品的子龙货号获取商品中心的spuid，skuid等信息
			icOut, err := client.QuerySkuIddBySkuThird(ctx, &params)
			conn.Close()
			cf()
			if err != nil {
				logger.Error(fmt.Sprintf("根据货号获取sku失败：%s;Sku_Third:%s", err.Error(), params.Sku_Third), err)
				continue
			}
			if icOut.SpuId == "0" {
				continue
			}

			//需要查询上架商品的skuid
			var _mapQty = dtos.GoodsStock{
				SpuId:   icOut.SpuId,
				GoodsId: icOut.SkuId, //根据Goodscode从彪彪那里获取sku，新版本全部统一
				Stock:   int(socksdetil.Ensalenum),
			}

			mapGoodsQty = append(mapGoodsQty, _mapQty)
			//当入参的 a8货号存在时,goodsId 是空的， 所以这里补上， 因为后面要删除对应的商品的库存信息
			if len(goodsCode) > 0 {
				goodsId = append(goodsId, icOut.SkuId)
			}

		}
	}
	logger.Info(logStr, ",goodsId=", kit.JsonEncode(goodsId))
	writeStock(warehouse, goodsId, mapGoodsQty)
	return
}

//处理redis的setnx的返回结果。如果锁定时间已经超过默认时间5分钟，则自动删除。默认时间可更改
func DelRedisSetNx(redisConn *redis.Client, redisKey string, timeMinute int32) bool {
	if redisConn.Exists(redisKey).Val() > 0 {
		timeUnix, _ := strconv.Atoi(redisConn.Get(redisKey).Val())
		//与当前时间比较
		timeNowUnix := time.Now().Add(-1 * time.Minute * 5).Unix() // 5分钟
		if timeMinute > 0 {
			timeDuration := time.Duration(-1*timeMinute) * time.Minute
			timeNowUnix = time.Now().Add(timeDuration).Unix()
		}
		if timeNowUnix >= int64(timeUnix) {
			//超过5分钟，则自动删除
			redisConn.Del(redisKey)
			return true
		}
		return false
	}
	return true
}

func getQuanQuDaoStockInfo(goodsCodes, warehouseCode string) {
	qqdCon := initQQD()
	params := new(dtos.StockGetRequest)
	params.Pagesize = 1
	params.Page = 1
	params.Iscontainwhs = true
	//仓库代码
	params.Whscode = warehouseCode
	signByte, _ := json.Marshal(params)
	signStr := string(signByte)
	//库存查询
	parStr := utils.Sign(signStr, "emall.stock.get", qqdCon.Token, qqdCon.AppkeyCon, qqdCon.AppsecretCon)
	bytesData, _ := json.Marshal(params)
	respBytes, err := utils.HttpPost(qqdCon.ApiUrl+parStr, bytesData, "application/json;charset=UTF-8", 0)
	logger.Info("lifasheng_check_qqd_stock-----获取库存请求参数：", string(bytesData), "。获取库存请求结果：", string(respBytes))
	if err != nil {
		logger.Error("获取库存请求参数：", string(bytesData), "。获取库存请求结果：", string(respBytes))
		panic(err)
	}
	retmes := new(dtos.StockGetResponse)
	err = json.Unmarshal(respBytes, retmes)
	if err != nil {
		logger.Error("无法格式化返回结果，错误原因：", err, "。数据集：", string(respBytes))
		panic(err)
	}
	//商品总条数
	qqdGoodsCount := retmes.Total
	//需要循环的次数
	forLen := 0
	//分页大小
	postSize := 100
	if qqdGoodsCount%postSize == 0 {
		forLen = qqdGoodsCount / postSize
	} else {
		forLen = qqdGoodsCount/postSize + 1
	}
	//设置每次查询100条
	params.Pagesize = postSize
	for i := 1; i <= forLen; i++ {
		//设置查询当前页
		params.Page = i
		signByte, _ := json.Marshal(params)
		signStr := string(signByte)
		//库存查询
		parStr := utils.Sign(signStr, "emall.stock.get", qqdCon.Token, qqdCon.AppkeyCon, qqdCon.AppsecretCon)
		bytesData, _ := json.Marshal(params)
		respBytes, err := utils.HttpPost(qqdCon.ApiUrl+parStr, bytesData, "application/json;charset=UTF-8", 0)
		logger.Info("lifasheng_check_qqd_stock-----获取库存请求参数：", string(bytesData), "。获取库存请求结果：", string(respBytes))
		if err != nil {
			logger.Error("拉取全渠道库存报错 " + err.Error())
			//panic(err)
			continue
		}
		retmes := new(dtos.StockGetResponse)
		err = json.Unmarshal([]byte(respBytes), retmes)
		if err != nil {
			logger.Error("格式化全渠道库存报错 " + err.Error())
			//panic(err)
			continue
		}
	}

}
